# 账户收益计算系统

这是一个完整的账户收益计算系统，支持两种不同格式的账户数据和两种交易类型（VWAP和T0）。

## 功能特性

### 🔧 数据标准化
- 支持Account 1 (CSV格式) 和Account 2 (Excel格式) 的数据标准化
- 自动处理不同的列名映射和数据类型转换
- 按账户类型独立的标准化函数

### 📊 收益计算
- 支持前日持仓、今日持仓的收益计算
- 支持VWAP和T0两种交易类型
- 自动获取收盘价数据进行计算
- 计算交易费用和税费

### 📈 日内交易分析
- 分析T0日内交易的买卖配对
- 计算已平仓收益和未平仓头寸
- 处理买卖数量不匹配的情况

### 💾 数据保存
- 自动保存标准化后的数据为CSV文件
- 保存计算结果为CSV文件
- 文件命名格式：`{类型}_{账户名}_{日期}.csv`

## 主要函数

### 数据标准化函数
```python
# Account 1 数据标准化
pre_hold = standardize_position_data_account1(position_df)
vwap_deal = standardize_transaction_data_account1(transaction_df, 'vwap')
t0_deal = standardize_transaction_data_account1(t0_transaction_df, 't0')

# Account 2 数据标准化
pre_hold = standardize_position_data_account2(position_df)
vwap_deal = standardize_transaction_data_account2(transaction_df, 'vwap')
t0_deal = standardize_transaction_data_account2(t0_transaction_df, 't0')
```

### 收益计算函数
```python
result = calc_account_estimate_pnl(
    date='********',
    pre_hold=pre_hold,           # 前日持仓
    hold=hold,                   # 今日持仓
    vwap_deal=vwap_deal,         # VWAP交易
    t0_deal=t0_deal,             # T0交易
    vwap_cr=0.0003,              # VWAP费率
    t0_cr=0.0005,                # T0费率
    tax_rate=0.001,              # 税率
    pre_close=pre_close,         # 前日收盘价（可选）
    close=close                  # 当日收盘价（可选）
)
```

### 一键处理函数
```python
# 处理单个账户的完整流程
result = process_account_data(
    account_name='account_1',
    account_type='account_1',
    date='********',
    data_path='/path/to/data',
    vwap_cr=0.0003,
    t0_cr=0.0005,
    tax_rate=0.001
)
```

## 输出指标

计算结果包含以下指标：

### 持仓相关
- `long_value`: 多头市值
- `short_value`: 空头市值
- `pre_long_value`: 前日多头市值
- `pre_short_value`: 前日空头市值

### 收益相关
- `gross_pnl`: 总收益
- `net_pnl`: 费后收益
- `long_pnl`: 多头收益
- `short_pnl`: 空头收益

### 交易金额
- `vwap_amount`: VWAP交易金额
- `t0_amount`: T0交易金额
- `total_amount`: 总交易金额

### 费用相关
- `vwap_commission`: VWAP手续费
- `t0_commission`: T0手续费
- `total_commission`: 总手续费
- `vwap_tax`: VWAP税费
- `t0_tax`: T0税费
- `total_tax`: 总税费

### 日内交易
- `t0_closed_pnl`: T0已平仓收益
- `t0_closed_amount`: T0已平仓交易金额
- `t0_open_positions_count`: 未平仓头寸数量
- `t0_open_volume`: 未平仓股数
- `t0_floating_pnl`: 未平仓浮动盈亏

## 生成的文件

运行后会生成以下文件：
- `hold_{账户名}_{日期}.csv`: 标准化持仓数据
- `vwap_deal_{账户名}_{日期}.csv`: 标准化VWAP交易数据
- `t0_deal_{账户名}_{日期}.csv`: 标准化T0交易数据
- `account_{账户名}_{日期}.csv`: 账户收益计算结果

## 使用示例

```python
# 运行测试
python calc_account_pnl.py

# 运行单元测试
python test_calc_account_pnl.py
```

## 数据格式要求

### Account 1 格式
- 持仓文件：CSV格式，包含列：证券代码、总持仓、可卖持仓
- 交易文件：CSV格式，包含列：证券代码、方向、成交价格、成交数量

### Account 2 格式
- 持仓文件：Excel格式，包含列：证券代码、持仓数量、子单可卖数量
- VWAP交易：CSV格式，包含列：证券代码、交易方向、成交均价、成交数量
- T0交易：CSV格式，包含列：股票、买卖、已成价格、已成量

## 注意事项

1. 收盘价数据会自动从数据库获取，如果获取失败会使用模拟数据
2. 持仓平衡检查：系统会检查前日持仓+交易=今日持仓的平衡性
3. 日内交易分析使用FIFO原则进行买卖配对
4. 所有金额计算精确到小数点后2位

## 测试覆盖

- ✅ 数据标准化功能测试
- ✅ 日内交易分析测试
- ✅ 收益计算功能测试
- ✅ 空数据处理测试
- ✅ 真实数据集成测试
