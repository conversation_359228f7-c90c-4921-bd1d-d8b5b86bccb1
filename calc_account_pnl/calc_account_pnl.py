from turtle import st
import pandas as pd
import numpy as np
import os, sys
import datetime
import warnings

warnings.filterwarnings('ignore')

misc_dir = '/home/<USER>/trade'
sys.path.insert(0, misc_dir)
try:
    from data_utils.trading_calendar import Calendar
    from misc.Readstockfile import update_xlsx_putdf
    from misc.tools import get_stock_close, get_index_constitutions_last_month
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("使用简化版本...")

    # 简化版Calendar类
    # class Calendar:
    #     @staticmethod
    #     def last_trading_day(date_str):
    #         from datetime import datetime, timedelta
    #         date = datetime.strptime(date_str, '%Y%m%d')
    #         # 简单地减去一天，实际应该考虑交易日历
    #         prev_date = date - timedelta(days=1)
    #         return prev_date

    # # 简化版get_stock_close函数
    # def get_stock_close(date):
    #     print(f"警告: 使用模拟收盘价数据 for {date}")
    #     return pd.DataFrame({
    #         'ticker': [1, 2, 600001, 300001],
    #         'close': [10.0, 15.0, 12.0, 25.0]
    #     })

sys.path.remove(misc_dir)



# 简化logger
class SimpleLogger:
    def warning(self, msg):
        print(f"WARNING: {msg}")
    def error(self, msg):
        print(f"ERROR: {msg}")

logger = SimpleLogger()

# date = '********'
# pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')

# pre_close = get_stock_close(pre_date)
# close = get_stock_close(date)



def standardize_position_data_account1(df):
    """
    标准化Account 1持仓数据格式

    Args:
        df: 原始持仓数据DataFrame

    Returns:
        标准化后的DataFrame，包含列: ['ticker', 'volume', 'available_volume']
    """
    standardized = pd.DataFrame()
    standardized['ticker'] = df['证券代码'].astype(str).str.replace(r'[^\d]', '', regex=True).astype(int)
    standardized['volume'] = df['总持仓'].fillna(0)
    standardized['available_volume'] = df['可卖持仓'].fillna(0)

    # 过滤掉持仓为0的记录
    standardized = standardized[standardized['volume'] != 0]
    standardized = standardized.reset_index(drop=True)

    return standardized


def standardize_position_data_account2(df):
    """
    标准化Account 2持仓数据格式

    Args:
        df: 原始持仓数据DataFrame

    Returns:
        标准化后的DataFrame，包含列: ['ticker', 'volume', 'available_volume']
    """
    standardized = pd.DataFrame()
    # 处理证券代码，可能包含.SH/.SZ后缀
    ticker_col = df['证券代码'].astype(str)
    # 提取数字部分
    standardized['ticker'] = ticker_col.str.replace(r'\.S[HZ]$', '', regex=True).astype(int)
    standardized['volume'] = df['持仓数量'].fillna(0)
    standardized['available_volume'] = df.get('子单可卖数量', df['持仓数量']).fillna(0)

    # 过滤掉持仓为0的记录
    standardized = standardized[standardized['volume'] != 0]
    standardized = standardized.reset_index(drop=True)

    return standardized


def standardize_position_data(df, account_type='account_1'):
    """
    标准化持仓数据格式（兼容性函数）
    """
    if account_type == 'account_1':
        return standardize_position_data_account1(df)
    elif account_type == 'account_2':
        return standardize_position_data_account2(df)
    else:
        raise ValueError(f"Unsupported account_type: {account_type}")


def standardize_transaction_data_account1(df, transaction_type='vwap'):
    """
    标准化Account 1交易数据格式

    Args:
        df: 原始交易数据DataFrame
        transaction_type: 交易类型 ('vwap' 或 't0')

    Returns:
        标准化后的DataFrame，包含列: ['ticker', 'direction', 'BS_flag', 'fill_price', 'fill_volume']
    """
    if df.empty:
        return pd.DataFrame(columns=['ticker', 'direction', 'BS_flag', 'fill_price', 'fill_volume'])

    standardized = pd.DataFrame()

    # Account 1 格式
    ticker_col = df['证券代码'].astype(str).str.replace(r'[^\d]', '', regex=True)
    standardized['ticker'] = ticker_col.astype(int)
    standardized['BS_flag'] = df['方向'].map({'买': '买入', '卖': '卖出'})
    # standardized.rename(columns={'成交价格': 'fill_price', '成交数量': 'fill_volume'}, inplace=True)
    # print(standardized.head())
    if df.dtypes['成交价格'] == 'object':
        standardized['fill_price'] = pd.to_numeric(df['成交价格'].str.replace(',', ''), errors='coerce')
    else:
        standardized['fill_price'] = pd.to_numeric(df['成交价格'], errors='raise')
    
    if df.dtypes['成交数量'] == 'object':
        standardized['fill_volume'] = pd.to_numeric(df['成交数量'].str.replace(',', ''), errors='coerce')
    else:
        standardized['fill_volume'] = pd.to_numeric(df['成交数量'], errors='raise')

    # 设置direction字段 (1: 多头, -1: 空头，这里简化处理，都设为1)
    # standardized['direction'] = 1

    # 过滤掉无效交易
    # standardized = standardized[
    #     (standardized['fill_volume'] > 0) &
    #     (standardized['fill_price'] > 0) &
    #     (standardized['BS_flag'].notna())
    # ].copy()

    standardized = standardized.reset_index(drop=True)

    return standardized


def standardize_transaction_data_account2(df, transaction_type='vwap'):
    """
    标准化Account 2交易数据格式

    Args:
        df: 原始交易数据DataFrame
        transaction_type: 交易类型 ('vwap' 或 't0')

    Returns:
        标准化后的DataFrame，包含列: ['ticker', 'direction', 'BS_flag', 'fill_price', 'fill_volume']
    """
    if df.empty:
        return pd.DataFrame(columns=['ticker', 'direction', 'BS_flag', 'fill_price', 'fill_volume'])

    standardized = pd.DataFrame()

    if transaction_type == 'vwap':
        # Account 2 VWAP交易格式
        ticker_col = df['证券代码'].astype(str).str.replace(r'\.S[HZ]$', '', regex=True)
        standardized['ticker'] = ticker_col.astype(int)
        standardized['BS_flag'] = df['交易方向'].map({'买入': '买入', '卖出': '卖出'})
        standardized['fill_price'] = pd.to_numeric(df['成交均价'], errors='raise')
        standardized['fill_volume'] = pd.to_numeric(df['成交数量'], errors='raise')
    else:  # t0
        # Account 2 T0交易格式
        ticker_col = df['股票'].astype(str).str.replace(r'\.S[HZ]$', '', regex=True)
        standardized['ticker'] = ticker_col.astype(int)
        standardized['BS_flag'] = df['买卖'].map({'买': '买入', '卖': '卖出'})
        standardized['fill_price'] = pd.to_numeric(df['已成价格'], errors='raise')
        standardized['fill_volume'] = pd.to_numeric(df['已成量'], errors='raise').abs()
        standardized = standardized[standardized['fill_volume'] != 0]

    # 设置direction字段 (1: 多头, -1: 空头，这里简化处理，都设为1)
    standardized['direction'] = 1

    # 过滤掉无效交易
    # standardized = standardized[
    #     (standardized['fill_volume'] > 0) &
    #     (standardized['fill_price'] > 0) &
    #     (standardized['BS_flag'].notna())
    # ].copy()

    standardized = standardized.reset_index(drop=True)

    return standardized


def standardize_transaction_data(df, account_type='account_1', transaction_type='vwap'):
    """
    标准化交易数据格式（兼容性函数）
    """
    if account_type == 'account_1':
        return standardize_transaction_data_account1(df, transaction_type)
    elif account_type == 'account_2':
        return standardize_transaction_data_account2(df, transaction_type)
    else:
        raise ValueError(f"Unsupported account_type: {account_type}")


class AccountInfo:
    def __init__(self, account_name, account_type, date, pre_hold, hold, vwap_deal, t0_deal, vwap_cr=0.00017, t0_cr=0.00027, tax_rate=0.0005, pre_close=None, close=None):
        self.account_name = account_name
        self.account_type = account_type
        self.date = date
        self.pre_date = Calendar.last_trading_day(self.date).strftime('%Y%m%d')
        self.pre_hold = pre_hold
        self.hold = hold
        self.vwap_deal = vwap_deal
        self.t0_deal = t0_deal
        self.vwap_cr = vwap_cr
        self.t0_cr = t0_cr
        self.tax_rate = tax_rate
        self.pre_close = pre_close
        self.close = close
        self.info = {}
        
        if self.pre_close is None or self.close is None:
            self.pre_date = Calendar.last_trading_day(self.date).strftime('%Y%m%d')
            if self.pre_close is None:
                self.pre_close = get_stock_close(self.pre_date)
            if self.close is None:
                self.close = get_stock_close(self.date)
        
        # 计算收益


    def calc_account_estimate_pnl(self):
        """
        计算账户收益

        Args:
            date: 交易日期 '********'
            pre_hold: 前日持仓 cols: ['ticker', 'volume', 'available_volume']
            hold: 今日持仓 cols: ['ticker', 'volume', 'available_volume']
            vwap_deal: VWAP交易 cols: ['ticker', 'direction', 'BS_flag', 'fill_price', 'fill_volume']
            t0_deal: T0交易 cols: ['ticker', 'direction', 'BS_flag', 'fill_price', 'fill_volume']
            vwap_cr: VWAP交易费率
            t0_cr: T0交易费率
            tax_rate: 税率
            pre_close: 前日收盘价 cols: ['ticker', 'close']
            close: 当日收盘价 cols: ['ticker', 'close']

        Returns:
            dict: 包含各项收益和统计指标
        """


        # 准备数据，添加direction字段
        pre_hold = self.pre_hold.copy()
        hold = self.hold.copy()

        # 设置direction字段（简化处理，根据volume正负判断）
        pre_hold['direction'] = pre_hold['volume'].apply(lambda x: 1 if x >= 0 else -1)
        pre_hold['volume'] = pre_hold['volume'].abs()

        hold['direction'] = hold['volume'].apply(lambda x: 1 if x >= 0 else -1)
        hold['volume'] = hold['volume'].abs()

        # 合并所有交易数据
        all_deals = []
        if self.vwap_deal is not None and not self.vwap_deal.empty:
            vwap_deal_copy = self.vwap_deal.copy()
            all_deals.append(vwap_deal_copy)

        if self.t0_deal is not None and not self.t0_deal.empty:
            # print(self.t0_deal.tail())
            t0_deal_copy = self.t0_deal.copy()
            all_deals.append(t0_deal_copy)

        if all_deals:
            deal = pd.concat(all_deals, ignore_index=True)
        else:
            deal = pd.DataFrame(columns=['ticker', 'direction', 'BS_flag', 'fill_price', 'fill_volume'])

        # 设置交易方向和开平标志
        if not deal.empty:
            deal['direction'] = deal['BS_flag'].map(lambda x: 1 if x in ['买入', '卖出'] else -1)
            deal['OC'] = deal['BS_flag'].map(lambda x: 1 if x in ['买入', '卖空'] else -1)
            # print(deal.head())
            deal['fill_volume'] = deal['fill_volume'].abs()

            # 过滤无效交易
            deal = deal[deal['fill_volume'] != 0]
            deal['fill_volume'] = deal['fill_volume'] * deal['OC']

        # 检查持仓平衡：前日持仓 + 交易 = 今日持仓
        if not deal.empty:
            tmp_deal = deal[['ticker', 'direction', 'fill_volume']].groupby(['ticker', 'direction']).sum()
            computed_hold = pre_hold.set_index(['ticker', 'direction'])['volume'].add(tmp_deal['fill_volume'], fill_value=0)
            diff_deal = hold.set_index(['ticker', 'direction'])['volume'].sub(computed_hold, fill_value=0)
            diff_deal = diff_deal[diff_deal != 0]

            if not (diff_deal == 0).all():
                diff_deal = diff_deal.to_frame('fill_volume').reset_index()
                diff_deal = pd.merge(diff_deal, self.close, on=['ticker'], how='left')
                diff_deal['OC'] = np.where(diff_deal['fill_volume'] > 0, 1, -1)
            
                logger.error(f'{self.account_name} morning_hold + deal != hold,  diff: \n{diff_deal}')
                confirm_flag = input("合并diff_deal 到 deal 中? y/n")
                if confirm_flag.lower() == 'y':
                    diff_deal['fill_price'] = diff_deal['close']
                    deal = pd.concat([deal, diff_deal], axis=0, ignore_index=True)

        # 分离开仓和平仓交易
        if not deal.empty:
            deal_open = deal[deal['OC']==1]
            deal_close = deal[deal['OC']==-1]

            # 计算开仓交易的加权平均价格
            if not deal_open.empty:
                deal_open = deal_open[['ticker', 'direction', 'fill_volume', 'fill_price']].groupby(['ticker', 'direction']).agg({
                    'fill_volume': 'sum',
                    'fill_price': lambda x: np.average(x, weights=deal_open.loc[x.index, "fill_volume"]),
                }).reset_index().rename(columns={'fill_volume': 'open_volume', 'fill_price': 'open_price'})
            else:
                deal_open = pd.DataFrame(columns=['ticker', 'direction', 'open_volume', 'open_price'])

            # 计算平仓交易的加权平均价格
            if not deal_close.empty:
                deal_close = deal_close[['ticker', 'direction', 'fill_volume', 'fill_price']].groupby(['ticker', 'direction']).agg({
                    'fill_volume': 'sum',
                    'fill_price': lambda x: np.average(x, weights=deal_close.loc[x.index, "fill_volume"]),
                }).reset_index().rename(columns={'fill_volume': 'close_volume', 'fill_price': 'close_price'})
            else:
                deal_close = pd.DataFrame(columns=['ticker', 'direction', 'close_volume', 'close_price'])
        else:
            deal_open = pd.DataFrame(columns=['ticker', 'direction', 'open_volume', 'open_price'])
            deal_close = pd.DataFrame(columns=['ticker', 'direction', 'close_volume', 'close_price'])

        # 合并数据
        data = pd.merge(hold, pre_hold, on=['ticker', 'direction'], how='outer', suffixes=('', '_pre'))
        data = pd.merge(data, deal_open, on=['ticker', 'direction'], how='outer')
        data = pd.merge(data, deal_close, on=['ticker', 'direction'], how='outer')

        # 合并收盘价数据
        data = pd.merge(data, self.close, on=['ticker'], how='left')
        data = pd.merge(data, self.pre_close.rename(columns={'close': 'close_pre'}), on=['ticker'], how='left')

        data = data.fillna(0)
        # print(data.columns)
        # print(data.head())

        info = self.info
        info['vwap_amount'] = vwap_deal_copy['fill_price'].mul(vwap_deal_copy['fill_volume']).sum()
        info['t0_amount'] = t0_deal_copy['fill_price'].mul(t0_deal_copy['fill_volume']).sum()
        info['total_amount'] = info['vwap_amount'] + info['t0_amount']

        # 分离多头和空头
        long = data[data['direction'] == 1]
        short = data[data['direction'] == -1]

        # 计算持仓市值
        info['long_value'] = long['volume'].mul(long['close']).sum()
        info['short_value'] = short['volume'].mul(short['close']).sum()
        info['pre_long_value'] = long['volume_pre'].mul(long['close_pre']).sum()
        info['pre_short_value'] = short['volume_pre'].mul(short['close_pre']).sum()

        # 计算持仓收益
        info['long_pnl'] = (
            long['volume'].mul(long['close']).sum()
            - long['volume_pre'].mul(long['close_pre']).sum()
            - long['open_volume'].mul(long['open_price']).sum()
            - long['close_volume'].mul(long['close_price']).sum()
        )
        info['short_pnl'] = - (
            short['volume'].mul(short['close']).sum()
            - short['volume_pre'].mul(short['close_pre']).sum()
            - short['open_volume'].mul(short['open_price']).sum()
            - short['close_volume'].mul(short['close_price']).sum()
        )

        info['gross_pnl'] = info['long_pnl'] + info['short_pnl']

        return info

    def analyze_intraday_trading(self):
        """
        分析日内交易，计算已平仓收益和未平仓头寸

        Args:
            t0_deal: 标准化的T0交易数据，包含列: ['ticker', 'direction', 'BS_flag', 'fill_price', 'fill_volume']

        Returns:
            dict: {
                'closed_pnl': 已平仓收益,
                'closed_amount': 已平仓交易金额,
                'open_positions': 未平仓头寸DataFrame,
                'open_floating_pnl': 未平仓浮动盈亏 (需要当前价格计算)
            }
        """
        info = self.info
        t0_deal = self.t0_deal.copy()
        
        # print(f't0_deal:{t0_deal.shape}')
        # print(f't0_deal:{t0_deal.head()}')
        if t0_deal.empty:
            info.update( {
                'closed_pnl': 0,
                'closed_amount': 0,
                'open_positions': pd.DataFrame(columns=['ticker', 'net_volume', 'avg_price']),
                'open_floating_pnl': 0
            })
            return

        closed_pnl = 0
        closed_amount = 0
        open_positions = []
        open_floating_pnl = 0

        # 按股票分组处理
        for ticker, group in t0_deal.groupby('ticker'):
            # 分离买入和卖出
            buys = group[group['BS_flag'] == '买入'].copy()
            sells = group[group['BS_flag'] == '卖出'].copy()

            if buys.empty and sells.empty:
                continue

            # 按价格排序，FIFO原则
            buys = buys.sort_values('fill_price').reset_index(drop=True)
            sells = sells.sort_values('fill_price', ascending=False).reset_index(drop=True)

            # 计算买入和卖出的总量
            total_buy_volume = buys['fill_volume'].sum()
            total_sell_volume = sells['fill_volume'].sum()

            # 计算平仓量（买卖的最小值）
            closed_volume = min(total_buy_volume, total_sell_volume)

            if closed_volume > 0:
                # 计算平仓收益
                # 使用加权平均价格
                buy_avg_price = np.average(buys['fill_price'], weights=buys['fill_volume'])
                sell_avg_price = np.average(sells['fill_price'], weights=sells['fill_volume'])

                # 平仓收益 = (卖出均价 - 买入均价) * 平仓量
                ticker_closed_pnl = (sell_avg_price - buy_avg_price) * closed_volume
                closed_pnl += ticker_closed_pnl

                # 平仓交易金额
                ticker_closed_amount = closed_volume * (buy_avg_price + sell_avg_price)
                closed_amount += ticker_closed_amount

            # 计算未平仓头寸

            net_volume = total_buy_volume - total_sell_volume
            if abs(net_volume) > 0:
                if net_volume > 0:
                    # 多头未平仓
                    
                    avg_price = np.average(buys['fill_price'], weights=buys['fill_volume'])
                elif net_volume < 0:
                    # 空头未平仓
                    avg_price = np.average(sells['fill_price'], weights=sells['fill_volume'])

                open_positions.append({
                    'ticker': ticker,
                    'net_volume': net_volume,
                    'avg_price': avg_price
                })

        print(f'closed_pnl:{closed_pnl}')

        if len(open_positions) == 0:
            open_positions_df = pd.DataFrame(columns=['ticker', 'net_volume', 'avg_price'])
        else:
            open_positions_df = pd.DataFrame(open_positions)
        # print(f'open_positions_df:{open_positions_df}')
        # print(f'self.close:{self.close}')
        open_positions_df = open_positions_df.merge(self.close, on='ticker', how='left')
        open_floating_pnl = open_positions_df['net_volume'].mul(open_positions_df['close'] - open_positions_df['avg_price']).sum()

        info.update({
            'closed_pnl': closed_pnl,
            'closed_amount': closed_amount,
            'open_positions': open_positions_df,
            'open_floating_pnl': open_floating_pnl
        })


    def calc_deal_cost(self):
        vwap_deal = self.vwap_deal.copy()
        t0_deal = self.t0_deal.copy()
        info = self.info
        
        # 计算交易费用
        info['vwap_commission'] = info['vwap_amount'] * self.vwap_cr
        info['t0_commission'] = info['t0_amount'] * self.t0_cr

        # 计算税费（只对卖出收税）
        vwap_sell = vwap_deal[vwap_deal['BS_flag'] == '卖出']
        vwap_tax = vwap_sell['fill_price'].mul(vwap_sell['fill_volume']).mul(self.tax_rate).abs().sum()
        t0_sell = t0_deal[t0_deal['BS_flag'] == '卖出']
        t0_tax = t0_sell['fill_price'].mul(t0_sell['fill_volume']).mul(self.tax_rate).abs().sum()
        info['vwap_tax'] = vwap_tax
        info['t0_tax'] = t0_tax
        
        info['vwap_cost'] = info['vwap_commission'] + info['vwap_tax']
        info['t0_cost'] = info['t0_commission'] + info['t0_tax']
        info['total_cost'] = info['vwap_cost'] + info['t0_cost']


    def account_stats(self):
        info = self.info
        
        # 计算总收益
        info['account_pnl_ac'] = info['gross_pnl'] - info['total_cost']
        info['t0_pnl_ac'] = info['closed_pnl'] - info['t0_commission'] - info['t0_tax']



    def save_standardized_data(pre_hold, hold, vwap_deal, t0_deal, result, date, account_name='account'):
        """
        保存标准化后的数据到CSV文件

        Args:
            pre_hold: 前日持仓数据
            hold: 今日持仓数据
            vwap_deal: VWAP交易数据
            t0_deal: T0交易数据
            result: 计算结果
            date: 日期
            account_name: 账户名称
        """
        try:
            # 保存持仓数据
            if not hold.empty:
                hold.to_csv(f'hold_{account_name}_{date}.csv', index=False, encoding='utf-8-sig')
                print(f"已保存: hold_{account_name}_{date}.csv")

            # 保存VWAP交易数据
            if not vwap_deal.empty:
                vwap_deal.to_csv(f'vwap_deal_{account_name}_{date}.csv', index=False, encoding='utf-8-sig')
                print(f"已保存: vwap_deal_{account_name}_{date}.csv")

            # 保存T0交易数据
            if not t0_deal.empty:
                t0_deal.to_csv(f't0_deal_{account_name}_{date}.csv', index=False, encoding='utf-8-sig')
                print(f"已保存: t0_deal_{account_name}_{date}.csv")

            # 保存账户收益结果
            result_df = pd.DataFrame([result])
            result_df.to_csv(f'account_{account_name}_{date}.csv', index=False, encoding='utf-8-sig')
            print(f"已保存: account_{account_name}_{date}.csv")

        except Exception as e:
            print(f"保存数据时出错: {e}")


def process_account_data(account_name, account_type, date, data_path, vwap_cr=0.0003, t0_cr=0.0005, tax_rate=0.001):
    """
    处理单个账户的数据并计算收益

    Args:
        account_name: 账户名称
        account_type: 账户类型 ('account_1' 或 'account_2')
        date: 交易日期
        data_path: 数据路径
        vwap_cr: VWAP费率
        t0_cr: T0费率
        tax_rate: 税率

    Returns:
        dict: 计算结果
    """
    try:
        # 获取前一交易日
        pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')

        # 获取收盘价
        pre_close = get_stock_close(pre_date)
        close = get_stock_close(date)

        print(f"\n=== 处理 {account_name} ({account_type}) ===")

        # 读取数据
        if account_type == 'account_1':
            # Account 1 格式
            pre_pos = pd.read_csv(f'{data_path}/{pre_date}/xtp_109156033251_Position.csv')
            curr_pos = pd.read_csv(f'{data_path}/{date}/xtp_109156033251_Position.csv')
            vwap_trans = pd.read_csv(f'{data_path}/{date}/transaction_{date}.csv')
            t0_trans = pd.read_csv(f'{data_path}/{date}/t0_transaction_{date}.csv')

            # 标准化数据
            pre_hold = standardize_position_data_account1(pre_pos)
            hold = standardize_position_data_account1(curr_pos)
            vwap_deal = standardize_transaction_data_account1(vwap_trans, 'vwap')
            t0_deal = standardize_transaction_data_account1(t0_trans, 't0')

        elif account_type == 'account_2':
            # Account 2 格式
            pre_pos = pd.read_excel(f'{data_path}/{pre_date}/position_{pre_date}.xlsx')
            curr_pos = pd.read_excel(f'{data_path}/{date}/position_{date}.xlsx')
            vwap_trans = pd.read_csv(f'{data_path}/{date}/algoActual.csv', encoding='gbk')
            t0_trans = pd.read_csv(f'{data_path}/{date}/t0_transaction_{date}.csv', encoding='gbk')

            # 标准化数据
            pre_hold = standardize_position_data_account2(pre_pos)
            hold = standardize_position_data_account2(curr_pos)
            vwap_deal = standardize_transaction_data_account2(vwap_trans, 'vwap')
            t0_deal = standardize_transaction_data_account2(t0_trans, 't0')

        else:
            raise ValueError(f"不支持的账户类型: {account_type}")

        print(f"前日持仓记录数: {len(pre_hold)}")
        print(f"当日持仓记录数: {len(hold)}")
        print(f"VWAP交易记录数: {len(vwap_deal)}")
        print(f"T0交易记录数: {len(t0_deal)}")

        # 计算收益
        A = AccountInfo(account_name, account_type, date, pre_hold, hold, vwap_deal, t0_deal, vwap_cr, t0_cr, tax_rate, pre_close, close)
        A.calc_account_estimate_pnl()
        A.analyze_intraday_trading()
        A.calc_deal_cost()
        A.account_stats()
    
        # 保存标准化数据
        # save_standardized_data(pre_hold, hold, vwap_deal, t0_deal, result, date, account_name)

        # 打印结果
        print(f"\n{account_name} 计算结果:")
        if not A.info['open_positions'].empty:
            print(f"  未平仓头寸:")
            print(A.info['open_positions'])
        
        for key, value in A.info.items():
            if isinstance(value, (int, float, np.integer, np.floating)):
                print(f"  {key.ljust(20)}: {value:,.2f}")
        
        return A.info

    except Exception as e:
        print(f"{account_name} 处理失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def account_pnl_calculation(date):
    """
    测试账户收益计算函数
    """
    print("=== 测试账户收益计算函数 ===")

    # Account 1
    result_1 = process_account_data(
        account_name='chaolz_zz1000',
        account_type='account_1',
        date=date,
        data_path=f'/data/shared-data/public/vsftp_data/zs_zhongtai/data/chaolz_zz1000/daily_after',
        vwap_cr=0.00017,
        t0_cr=0.00027,
        tax_rate=0.0005
    )

    # Account 2
    result_2 = process_account_data(
        account_name='duichong',
        account_type='account_2',
        date=date,
        data_path='/data/shared-data/public/vsftp_data/zs_zhongtai/data/duichong/daily_after',
        vwap_cr=0.00017,
        t0_cr=0.00027,
        tax_rate=0.0005
    )


if __name__ == "__main__":
    if len(sys.argv) > 1:
        date = str(sys.argv[1])
    else:        
        date = datetime.datetime.now().strftime('%Y%m%d')
    account_pnl_calculation(date)
    
    
    
    
    
    
    
    
    
    
    