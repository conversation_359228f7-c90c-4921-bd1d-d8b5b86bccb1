import datetime
from operator import index
from anyio import wrap_file
from narwhals import Date
from misc.tools import find_latest_remote_file_on_date, get_stock_adj_close
from misc.ssh_conn import sftp_clent_zsdav, sftp_clent_wintrader
from misc.Readstockfile import update_remote_xlsx_getdf, update_remote_xlsx_putdf, read_remote_file, write_file

import os, sys

from misc.tools import get_index_constitutions_last_month, get_cons_tag_series
# name = find_latest_remote_file_on_date(
#     sftp_method=sftp_clent_zsdav,
#     dir= '超量子中泰/hold',
#     file_prefix='hold_',
#     date='20250610'
# )
# print(name)


# path = 'clz_zz1000账户超额带公式.xlsx'

# df = update_remote_xlsx_getdf(path, src_type='zsdav', sheet_name='clz_zz1000')
# df['test'] = 'test'
# print(df)
# update_remote_xlsx_putdf(df, file_type='xlsx', dest_type='zsdav', dest_path=path, sheet_name='clz_zz1000', index=False)



# get current working dir
# cur_dir = os.path.dirname(os.path.abspath(__file__))
# print(cur_dir)
# # get python path
# python_path = sys.path[0]
# print(python_path)

from misc.tools import get_last_month_last_trading_day, get_stock_close

import pandas as pd
import numpy as np
# file = f'1000指数成分.xlsx'

# total_value = 30000000

# for date in [
#     '20240101',
#     '20240201',    
#     '20240301',    
#     '20240401',    
#     '20240501',    
#     '20240601',    
#     '20240701',    
#     '20240801',    
#     '20240901',    
#     '20241001',    
#     '20241101',    
#     '20241201',    
#     # '20250101',    
# ]:
# # date = '20240201'
#     df = get_index_constitutions_last_month('000852', date)
#     pre_date = get_last_month_last_trading_day(date)
#     close = get_stock_close(pre_date)
#     close['ticker'] = close['ticker'].astype(str).str.zfill(6)
#     df = df.merge(close, on='ticker', how='left')
#     df['volume'] = np.floor(df['WEIGHT']/100 * total_value / df['close'] /100).astype(int) * 100
    
    

#     try:
#         with pd.ExcelWriter(file, engine='openpyxl', mode='a') as writer:
#             df.to_excel(writer, sheet_name=date, index=False)
#     except FileNotFoundError:
#         with pd.ExcelWriter(file, engine='openpyxl', mode='w') as writer:
#             df.to_excel(writer, sheet_name=date, index=False)

#     # print(close)
#     print(df)


# from misc.tools import get_stock_adj_close

# df = get_stock_adj_close('20250612')

# print(df.sort_values('ticker', ascending=False).head(10))

# import pickle
# def pickle_dump(data,pt):
#     with open(pt, 'wb') as file:
#         pickle.dump(data, file)

# def pickle_load(pt):
#     with open(pt, 'rb') as file:
#         return pickle.load(file)
    
# file = '/home/<USER>/trade/t0_backtest/intraday/inventory/zz2000_pos_0940.pkl'

# df = pickle_load(file)

# print(type(df))
# # print(df.head(10))

# # df.head(10).to_csv('zz2000_pos_sample.csv', index=False)
# print(list(df.keys())[0])
# # print(df.keys())


# df = pd.DataFrame.from_dict(df, 'index')
# df = df.head(10)
# df.to_csv('zz2000_pos_sample.csv', index=False)


# file = '数据导出/中泰SmartX/自动导出/20250617/xtp_109156033251_Position.csv'


# df = read_remote_file(file, src_type='wintrader')
# print(df.head(10))

# write_file(df, file_type='csv', dest_type='dav', dest_path='xtp_109156033251_Position.csv', index=False)

# df = df.rename(columns={'总持仓: ''})
# df = get_stock_adj_close('20250617')
# print(df.head(3))


# from misc.ssh_conn import sftp_clent_zt_tuoguan


# file = 'test.md'
# sftp_clent_zt_tuoguan.put(file, os.path.join('daily_before/target', file))

# from misc.tools import get_stock_adj_close

# df = get_stock_adj_close('20250620')

# df['ticker'] = df['ticker'].astype(str).str.zfill(6)

# df.to_excel('price_20250620.xlsx')

# from misc.ssh_conn import ftp_clent_zx_zhongtai

# d = '20250626'
# dir = '/data/chaolz_zz1000/daily_after/20250626'

# ftp_clent_zx_zhongtai.mkdir(dir)

# from misc.tools import get_stock_adj_close
# df = get_stock_adj_close('20250625')
# print(df)

from data_utils.trading_calendar import TradingCalendar, Calendar


def get_last_trading_days_of_weeks(start_date, end_date):
    """
    获取指定日期范围内每个星期的最后一个交易日
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
        trading_calendar: TradingCalendar实例
        
    Returns:
        list: 每个星期的最后一个交易日的日期列表
    """
    # 获取所有交易日
    all_trading_days = Calendar.trading_dates(start_date, end_date)
    
    if not all_trading_days:
        return []
    
    # 转换为DataFrame以便使用pandas功能
    df = pd.DataFrame({'date': all_trading_days})
    
    # 确保date列是datetime类型(虽然你的TradingCalendar已经返回date对象)
    df['date'] = pd.to_datetime(df['date'])
    
    # 按周分组(周一为一周的第一天)
    df['year'] = df['date'].dt.isocalendar().year
    df['week'] = df['date'].dt.isocalendar().week
    
    # 按年和周分组，并取每组的最后一天
    last_trading_days = df.groupby(['year', 'week'])['date'].max().dt.date.tolist()
    last_trading_days = [day.strftime('%Y%m%d') for day in last_trading_days]
    
    return last_trading_days

from misc.tools import get_cons_tag_series



# 使用示例
if __name__ == "__main__":
    # # 初始化日历(假设holiday_dates已正确加载)
    # import datetime
    # start_date = datetime.date(2025, 1, 1)
    # end_date = datetime.date(2025, 6, 30)
    
    # last_trading_days = get_last_trading_days_of_weeks(start_date, end_date)
    # print("每个星期的最后一个交易日:")
    # for day in last_trading_days:
    #     print(day)
    
    # df = get_stock_adj_close('20250623')
    # df = get_cons_tag_series('20250623')
    # print(df)

    
    index_code = '000300'
    last_month_last_trading_day = '20250630'
    
    sql2 = f"""
    SELECT 
        c.TICKER_SYMBOL AS ticker,
        c.SEC_SHORT_NAME AS STOCK_NAME,
        a.EFF_DATE,
        a.WEIGHT
    FROM csi_idxm_wt_ashare a 
    LEFT JOIN md_security c ON a.CONS_ID = c.SECURITY_ID
    WHERE EXISTS (
        SELECT 1 
        FROM md_security b 
        WHERE b.SECURITY_ID = a.SECURITY_ID 
        AND b.TICKER_SYMBOL = '{index_code}'
    )
    AND a.EFF_DATE = '{last_month_last_trading_day}'
        ORDER BY c.TICKER_SYMBOL
    """
    from misc.tools import DATAYESDB_CONNECTION_STRING
    from sqlalchemy import create_engine
    engine = create_engine(DATAYESDB_CONNECTION_STRING)
    
    # df2 = pd.read_sql(sql2, engine)
    # print(df2)
    
    
    sql1 = f"""
        SELECT 
            c.TICKER_SYMBOL AS ticker,
            c.SEC_SHORT_NAME AS STOCK_NAME,
            a.EFF_DATE,
            a.WEIGHT
        FROM csi_idxm_wt_ashare a 
            LEFT JOIN md_security b ON a.SECURITY_ID=b.SECURITY_ID
            LEFT JOIN md_security c ON a.CONS_ID=c.SECURITY_ID
        WHERE b.TICKER_SYMBOL='{index_code}' /*输入需查询的指数代码*/
            AND a.EFF_DATE='{last_month_last_trading_day}'/*输入需查询的生效日期*/
        ORDER BY c.TICKER_SYMBOL
    """
    # df1 = pd.read_sql(sql1, engine)
    # print(df1)
    
    
    # print(df1.equals(df2))
    
    
# from misc.tools import get_turnover_rate_by_date

# df = get_turnover_rate_by_date('20250617')


# print(df.head(3))
# print(
# df['turnover_rate'].describe()
# )

# t = datetime.datetime.now().date()
# t = datetime.datetime.combine(t, datetime.time.min)
# print(t.timestamp())

# print(int(t.timestamp() * 1000))

# div_file = '/home/<USER>/trade/futures_monitor/futures_div_estimate.csv'
# df = pd.read_csv(div_file)

# print('records')
# print(df.to_dict('records'))
# print('')

# print('index')
# print(df.to_dict('index'))
# print('')

# print('dict')
# print(df.to_dict('dict'))
# print('')

# print('list')
# print(df.to_dict('list'))
# print('')
# print('series')
# print(df.to_dict('series'))
# print('')
# print('tight')
# print(df.to_dict('tight'))
# print('')
# print('split')
# print(df.to_dict('split'))


import akshare as ak

# futures_zh_realtime_df = ak.futures_zh_realtime(symbol="中证500指数期货")
# print(futures_zh_realtime_df)

# print(futures_zh_realtime_df['settlement'])

# df = ak.futures_symbol_mark()
# print(df['symbol'].tolist())


import pandas as pd

d = ['20250617 15:00:00', '20250617 15:01:00', '20250617 15:02:00', '20250617 15:03:00', '20250617 15:04:00']

df = pd.DataFrame({'date': d})
print(df)
df['date'] = pd.to_datetime(df['date'], format='%Y%m%d %H:%M:%S')
print(df)

df['date'] = df['date'].astype('int64')
print(df)
# di = df.as_unit('D')
# print(di)

# di = df.as_unit('h')
# print(di)

# di = df.as_unit('m')
# print(di)

# di = df.as_unit('s')
# print(di)

# di = df.as_unit('ms')
# print(di)

# di = df.asm8
# print(di)

# di = df.timestamp()
# print(di)

# di = df.astype('int64')
# print(di)

# translate datetime to timestamp
# df['date'] = df['date'].
# print(df)

