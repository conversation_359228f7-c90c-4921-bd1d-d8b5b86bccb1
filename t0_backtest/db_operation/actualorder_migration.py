#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据迁移脚本：从 trading_data.parentorders 迁移到 zs_trading_data.algo_parentorder
"""

from tracemalloc import start
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os
import logging
from typing import Optional
import warnings

warnings.filterwarnings('ignore')


# 添加utils路径
sys.path.append('/home/<USER>/trade/t0_backtest/intraday/utils')
from mysql import get_kf_trading_data_db_connection, get_zs_trading_data_db_connection, query, upsert_dataframe

sys.path.append('/home/<USER>/trade')
from data_utils.trading_calendar import Calendar


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('actualorder_migration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def timestamp_to_hhmmss(timestamp_ms):
    """
    将13位时间戳转换为HHMMSS格式
    
    Args:
        timestamp_ms: 13位时间戳（毫秒）
    
    Returns:
        int: HHMMSS格式的时间，如143000表示14:30:00
    """
    if pd.isna(timestamp_ms) or timestamp_ms == '' or timestamp_ms is None:
        return None

    try:
        # 转换为秒级时间戳
        timestamp_s = int(timestamp_ms) / 1000

        # 转换为datetime对象（默认是UTC时间）
        dt = datetime.fromtimestamp(timestamp_s)

        # 格式化为HHMMSS
        hhmmss = int(dt.strftime('%H%M%S'))

        return hhmmss


    except (ValueError, TypeError, OSError) as e:
        print(f"时间戳转换错误: {timestamp_ms}, 错误: {e}")
        return None


def timestamp_to_hhmmss_adjust_cst(timestamp_ms):
    """
    将13位时间戳转换为HHMMSS格式
    
    Args:
        timestamp_ms: 13位时间戳（毫秒）
    
    Returns:
        int: HHMMSS格式的时间，如143000表示14:30:00
    """
    if pd.isna(timestamp_ms) or timestamp_ms == '' or timestamp_ms is None:
        return None

    try:
        # 转换为秒级时间戳
        timestamp_s = int(timestamp_ms) / 1000

        # 转换为datetime对象（默认是UTC时间）
        dt = datetime.fromtimestamp(timestamp_s)

        # 提取小时部分
        hour = dt.hour

        # 如果小时小于9或大于14，增加8小时（调整为中国时区）
        if hour < 9 or hour > 15:
            dt += timedelta(hours=8)

        # 格式化为HHMMSS
        hhmmss = int(dt.strftime('%H%M%S'))
        # if hour < 9 or hour > 15:
        #     print(f'时间戳转换处理：{timestamp_ms} -> {dt}, {hhmmss}')

        return hhmmss


    except (ValueError, TypeError, OSError) as e:
        print(f"时间戳转换错误: {timestamp_ms}, 错误: {e}")
        return None


def operation_mapping(operation):
    """
    operation字段映射
    
    Args:
        operation: 原始operation值
    
    Returns:
        int: 映射后的operation值
    """
    mapping = {
        100: 0,  # 买入
        110: 1   # 卖出
    }
    return mapping.get(operation, operation)

def test_timestamp_conversion():
    """测试时间戳转换函数"""
    test_cases = [
        1697507007466,  # 2023-10-17 10:30:07
        1697507176196,  # 2023-10-17 10:32:56
        1697507006543,  # 2023-10-17 10:30:06
        1697508806543,  # 2023-10-17 11:00:06
    ]
    
    print("测试时间戳转换:")
    for ts in test_cases:
        result = timestamp_to_hhmmss(ts)
        dt = datetime.fromtimestamp(ts / 1000)
        print(f"时间戳: {ts} -> {result} (原时间: {dt})")

def test_operation_mapping():
    """测试operation映射函数"""
    test_cases = [100, 110]
    
    print("\n测试operation映射:")
    for op in test_cases:
        result = operation_mapping(op)
        print(f"Operation: {op} -> {result}")


sys_type_mapping = {
    'HX_SMART_VWAP' : 'gtrade',
    'KF_VWAP_CORE' : 'kafang',
    'kf_vwap_plus' : 'kafang',
    'XT_VWAPA1' : 'xuntou',
    'kf_twap_plus' : 'kafang',
}

broker_mapping = {
    'HX_SMART_VWAP' : 'guoxin',
    'KF_VWAP_CORE' : 'kafang',
    'kf_vwap_plus' : 'kafang',
    'kf_twap_plus' : 'kafang',
}


def transform_parentorder_data(df):
    """
    转换parentorder数据格式

    Args:
        df: 源数据DataFrame (trading_data.parentorders格式)

    Returns:
        DataFrame: 转换后的数据 (zs_trading_data.algo_parentorder格式)
    """
    if df.empty:
        print("输入数据为空")
        return pd.DataFrame()

    print(f"开始转换数据，共 {len(df)} 条记录")

    # 创建新的DataFrame
    new_df = pd.DataFrame()

    # 直接映射的字段
    new_df['id'] = df['id']
    new_df['symbol'] = df['symbol']
    new_df['quantity'] = df['quantity'].astype(int)
    
    df['filled_quantity'] = df['filled_quantity'].fillna(0)
    new_df['filled_quantity'] = df['filled_quantity'].astype(int)
    new_df['filled_price'] = df['filled_price']
    new_df['account_id'] = df['account_id']
    new_df['algo_name'] = df['algo_name']
    new_df['firm'] = df['firm']
    new_df['algo_provider'] = df['algo_provider']
    # params字段设置为空字符串（目标表字段长度限制）
    new_df['params'] = df['params'].fillna('')

    # 需要转换的字段
    new_df['operation'] = df['operation'].apply(operation_mapping)
    
    df['tmp_create_time'] = df['create_time'].apply
    
    new_df['create_time'] = df['create_time'].apply(timestamp_to_hhmmss_adjust_cst)
    new_df['start_time'] = df['start_time'].apply(timestamp_to_hhmmss)
    new_df['end_time'] = df['end_time'].apply(timestamp_to_hhmmss)

    # date字段转换为整数
    new_df['date'] = df['date'].astype(int)

    # 设置默认值
    new_df['pm'] = ''
    new_df['broker']   = df['algo_name'].apply(lambda x: broker_mapping.get(x, 'xuntou_gt'))
    new_df['sys_type'] = df['algo_name'].apply(lambda x: sys_type_mapping.get(x, 'xuntou_gt'))
    new_df['remark1'] = ''
    new_df['remark2'] = ''
    new_df['remark3'] = ''

    print(f"数据转换完成，转换后 {len(new_df)} 条记录")
    return new_df

def migrate_parentorder_data(date_filter: Optional[str] = None, limit: Optional[int] = None,
                           batch_size: int = 1000, dry_run: bool = False):
    """
    执行数据迁移

    Args:
        date_filter: 日期过滤条件，如 '20231017'
        limit: 限制记录数量，用于测试
        batch_size: 批量处理大小
        dry_run: 是否为试运行（不实际写入数据）
    """
    logger.info("开始数据迁移...")
    logger.info(f"参数: date_filter={date_filter}, limit={limit}, batch_size={batch_size}, dry_run={dry_run}")

    source_conn = None
    target_engine = None

    try:
        # 连接源数据库
        logger.info("连接源数据库...")
        source_conn = get_kf_trading_data_db_connection()

        # 构建查询SQL
        sql = "SELECT * FROM orders"
        conditions = []

        if date_filter:
            conditions.append(f"date = '{date_filter}'")

        if conditions:
            sql += " WHERE " + " AND ".join(conditions)

        if limit:
            sql += f" LIMIT {limit}"

        logger.info(f"执行查询: {sql}")

        # 读取源数据
        source_df = query(source_conn, sql)
        logger.info(f"从源数据库读取到 {len(source_df)} 条记录")

        if source_df.empty:
            logger.warning("没有找到符合条件的数据")
            return 0

        # 显示源数据样例
        logger.info("源数据样例:")
        logger.info(f"\n{source_df.head(2).to_string()}")

        # 数据质量检查
        logger.info("执行数据质量检查...")
        check_data_quality(source_df)

        # 转换数据
        logger.info("开始数据转换...")
        target_df = transform_parentorder_data(source_df)

        if target_df.empty:
            logger.error("数据转换后为空")
            return 0

        # 显示转换后数据样例
        logger.info("转换后数据样例:")
        logger.info(f"\n{target_df.head(2).to_string()}")

        if dry_run:
            logger.info("试运行模式，不写入数据库")
            return len(target_df)

        # 连接目标数据库并写入数据
        logger.info("连接目标数据库...")
        target_engine = get_zs_trading_data_db_connection(engine=True)

        # 批量写入数据
        total_records = len(target_df)
        processed_records = 0

        for i in range(0, total_records, batch_size):
            batch_df = target_df.iloc[i:i+batch_size]
            logger.info(f"写入批次 {i//batch_size + 1}: {len(batch_df)} 条记录")

            upsert_dataframe(target_engine, batch_df, "algo_parentorder",
                           {'id': tuple(batch_df['id'].values)})

            processed_records += len(batch_df)
            logger.info(f"已处理 {processed_records}/{total_records} 条记录")

        logger.info("数据迁移完成!")
        return processed_records

    except Exception as e:
        logger.error(f"数据迁移过程中发生错误: {e}")
        raise
    finally:
        if source_conn:
            source_conn.close()
            logger.info("已关闭源数据库连接")

def check_data_quality(df):
    """
    检查数据质量

    Args:
        df: 源数据DataFrame
    """
    logger.info("=== 数据质量检查 ===")

    # 检查必要字段是否存在
    required_fields = ['id', 'symbol', 'quantity', 'operation', 'date']
    missing_fields = [field for field in required_fields if field not in df.columns]
    if missing_fields:
        raise ValueError(f"缺少必要字段: {missing_fields}")

    # 检查空值
    null_counts = df[required_fields].isnull().sum()
    if null_counts.any():
        logger.warning(f"发现空值: \n{null_counts[null_counts > 0]}")

    # 检查operation字段值
    invalid_operations = df[~df['operation'].isin([100, 110])]['operation'].unique()
    if len(invalid_operations) > 0:
        logger.warning(f"发现无效的operation值: {invalid_operations}")

    # 检查数量字段
    if (df['quantity'] <= 0).any():
        logger.warning("发现quantity <= 0的记录")

    logger.info("数据质量检查完成")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='ParentOrder数据迁移工具')
    # parser.add_argument('--date', type=str, help='指定日期过滤，格式：20231017')
    parser.add_argument('--limit', type=int, help='限制处理记录数量（用于测试）')
    parser.add_argument('--batch-size', type=int, default=5000, help='批量处理大小')
    parser.add_argument('--dry-run', action='store_true', help='试运行模式，不实际写入数据')
    parser.add_argument('--test', action='store_true', help='运行测试函数')

    args = parser.parse_args()
    
    
    start_date = '20231017'
    end_date   = '20250207'
    dates = [d.strftime('%Y%m%d') for d in Calendar.trading_dates(start_date, end_date)]
    print(f'日期范围: {start_date} ~ {end_date}')

    if args.test:
        print("=== 测试转换函数 ===")
        test_timestamp_conversion()
        test_operation_mapping()
        return

    try:
        for date in dates:
            processed_count = migrate_parentorder_data(
                date_filter=date,
                limit=args.limit,
                batch_size=args.batch_size,
                dry_run=args.dry_run
            )
            logger.info(f"迁移 {date} 完成，共处理 {processed_count} 条记录")
    except Exception as e:
        logger.error(f"{date} 迁移失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
