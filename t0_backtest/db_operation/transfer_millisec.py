import sys
import pandas as pd
import numpy as np
import mysql.connector
from mysql.connector import Error


# 添加utils路径
# sys.path.append('/home/<USER>/trade/t0_backtest/intraday/utils')
# from mysql import get_kf_trading_data_db_connection, get_zs_trading_data_db_connection, query, upsert_dataframe

# sys.path.append('/home/<USER>/trade')
# from data_utils.trading_calendar import Calendar





def connect_to_mysql(host, user, password, database):
    """
    连接到 MySQL 数据库
    """
    try:
        connection = mysql.connector.connect(
            host=host,
            user=user,
            password=password,
            database=database
        )
        if connection.is_connected():
            print("成功连接到 MySQL 数据库")
            return connection
    except Error as e:
        print(f"连接数据库时出错: '{e}'")
        return None
    
    

def update_create_time_to_milliseconds(connection):
    """
    将 orders 表中的 create_time 从10位时间戳更新为13位时间戳
    """
    if connection is None:
        print("数据库连接未建立，无法执行更新操作。")
        return
    

    cursor = connection.cursor()

    # 开启事务
    connection.start_transaction()
    

    try:
        # 查询所有记录的 id 和 create_time
        select_query = """
        SELECT id, create_time FROM orders
        WHERE create_time < 1000000000000
        """
        cursor.execute(select_query)
        records = cursor.fetchall()

        if not records:
            print("orders 表中没有记录需要更新。")
            return

        # 准备更新语句
        update_query = "UPDATE orders SET create_time = %s WHERE id = %s"

        # 遍历每一条记录，转换时间戳并执行更新
        updated_count = 0
        for record in records:
            record_id, create_time_10 = record
            # 将10位时间戳转换为13位时间戳
            create_time_13 = create_time_10 * 1000  # 秒 -> 毫秒

            # 执行更新
            cursor.execute(update_query, (create_time_13, record_id))
            updated_count += 1
            
            # 可选：每更新一定数量的记录后打印进度
            if updated_count % 1000 == 0:
                print(f'create_time_10: {create_time_10}')
                print(f'create_time_13: {create_time_13}')
                print(f'record_id: {record_id}')
                print(f"已更新 {updated_count} 条记录...")

        # 提交事务
        connection.commit()
        print(f"成功更新了 {updated_count} 条记录的 create_time 为13位时间戳。")

    except Exception as e:
        # 发生错误时回滚事务
        connection.rollback()
        print(f"更新过程中出错: '{e}'。已回滚事务。")
    finally:
        # 关闭游标
        cursor.close()

def main():
    # 数据库连接参数
    host = '************'       # 替换为你的 MySQL 主机地址
    user = 'root'               # 替换为你的 MySQL 用户名
    password = 'jtwmy,dt4gx'           # 替换为你的 MySQL 密码
    database = 'zs_trading_data'   # 替换为你的数据库名称

    # 连接到 MySQL
    connection = connect_to_mysql(host, user, password, database)
    
    # connection = get_zs_trading_data_db_connection(engine=True)
    if connection:
        # 执行更新操作
        update_create_time_to_milliseconds(connection)
        # 关闭连接
        connection.close()
        print("数据库连接已关闭。")

if __name__ == "__main__":
    main()