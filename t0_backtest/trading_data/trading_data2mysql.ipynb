{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/trade/data_sync_algo\n", "/home/<USER>/trade\n"]}], "source": ["import pandas as pd\n", "import datetime\n", "import os, sys\n", "from pathlib import Path\n", "\n", "notebook_dir = Path.cwd() \n", "\n", "print(str(notebook_dir.parent))\n", "sys.path.insert(0, str(notebook_dir.parent))\n", "from utils import mysql\n", "sys.path.remove(str(notebook_dir.parent))\n", "\n", "\n", "print(str(notebook_dir.parent.parent))\n", "sys.path.insert(0, str(notebook_dir.parent.parent))\n", "from misc.ssh_conn import ftp_clent_zx_zhongtai\n", "from misc.Readstockfile import read_remote_file, write_file\n", "sys.path.remove(str(notebook_dir.parent.parent))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["account_firm_map={'28131077_3104_3104':'kuanfu','9225553_1102_1102':'wuzhi'}\n", "def int2str(x):\n", "    return str(int(x))\n", "\n", "def operation_map(x):\n", "    if x==1:\n", "        return 0\n", "    if x==2:\n", "        return 1\n", "    return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 国君总线数据\n", "### 日内母单\n", "- id ,symbol ,quantity,create_time,start_time,end_time,algo_name,account_id,firm ,algo_provider,pm,date,broker,params,sys_type,remark1,remark2,remark3\n", "### 拆单母单\n", "- id ,symbol ,quantity,operation,create_time,start_time,end_time,algo_name,account_id,firm ,algo_provider,pm,date,broker,params,sys_type,status,remark1,remark2,remark3\n", "### 委托（子单）\n", "- id,pid,symbol,create_time,last_upd_time,account_id,operation,price,quantity,filled_price,filled_quantity,status,order_id,order_type,err_msg,remark1,remark2\n", "\n", "///未知\n", "const char FS_OST_Unknown = '0';\n", "///未报\n", "const char FS_OST_Cached = '1';\n", "///已报\n", "const char FS_OST_Accepted = '2';\n", "///部分成交\n", "const char FS_OST_PartTraded = '3';\n", "///停止 （非终态，可恢复）\n", "const char FS_OST_Stop = '4';\n", "///撤销中\n", "const char FS_OST_PendingCancel = '5';\n", "///全部成交\n", "const char FS_OST_AllTraded = '6';\n", "///被动撤单\n", "const char FS_OST_EXPIRED = '7';\n", "///主动撤单\n", "const char FS_OST_Canceled = '8';\n", "///已拒绝\n", "const char FS_OST_Rejected = '9';"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ld guojun trading algo parentorder"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["********\n"]}], "source": ["# date=\"********\"\n", "date=datetime.datetime.now().strftime('%Y%m%d')\n", "print(date)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# df=pd.read_csv(r\"/home/<USER>/py/stk_py/data/raw_data/data/ld_guojun/tb_tdsest_strategyorder_{}.csv\".format(date))\n", "df=pd.read_csv(r\"/home/<USER>/dav/ld_guojun/tb_tdsest_strategyorder_{}.csv\".format(date))\n", "df=df[df['strategy_exec_broker']=='ldsmart01']"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          date                       id  operation  create_time  start_time  \\\n", "0     ********  guojun_ld_********10000          1        91843       94000   \n", "1     ********  guojun_ld_********10001          1        91843       94000   \n", "2     ********  guojun_ld_********10002          1        91843       94000   \n", "3     ********  guojun_ld_********10003          1        91843       94000   \n", "4     ********  guojun_ld_********10004          1        91843       94000   \n", "...        ...                      ...        ...          ...         ...   \n", "1024  ********  guojun_ld_********11024          1       140950      140950   \n", "1025  ********  guojun_ld_********11025          1       140950      140950   \n", "1026  ********  guojun_ld_********11026          0       140950      140950   \n", "1027  ********  guojun_ld_********11027          1       140950      140950   \n", "1028  ********  guojun_ld_********11028          0       140950      140950   \n", "\n", "      end_time  symbol  quantity  filled_quantity  filled_price  ...  \\\n", "0       101000  300550       200              200     12.980000  ...   \n", "1       101000  600020       100              100      4.950000  ...   \n", "2       101000  300553       300              300     47.423333  ...   \n", "3       101000  300565      1300             1300     12.047692  ...   \n", "4       101000  300515       400              400     18.320000  ...   \n", "...        ...     ...       ...              ...           ...  ...   \n", "1024    143600  601211       100              100     18.470000  ...   \n", "1025    143600  601009       100              100     11.450000  ...   \n", "1026    143600  600331       300              300      7.460000  ...   \n", "1027    143600  002300       600                0           NaN  ...   \n", "1028    143600  300688       200              200     29.720000  ...   \n", "\n", "     algo_name    firm algo_provider   pm       broker params  \\\n", "0         vwap   wuzhi        zhishu  xuj  guo<PERSON><PERSON>an          \n", "1         vwap   wuzhi        zhishu  xuj  guo<PERSON><PERSON>an          \n", "2         vwap   wuzhi        zhishu  xuj  guo<PERSON><PERSON>an          \n", "3         vwap   wuzhi        zhishu  xuj  guo<PERSON><PERSON>an          \n", "4         vwap   wuzhi        zhishu  xuj  guo<PERSON><PERSON>an          \n", "...        ...     ...           ...  ...          ...    ...   \n", "1024      vwap  kuanfu        zhishu  xuj  guo<PERSON>an          \n", "1025      vwap  kuanfu        zhishu  xuj  guo<PERSON>an          \n", "1026      vwap  kuanfu        zhishu  xuj  guo<PERSON>an          \n", "1027      vwap  kuanfu        zhishu  xuj  guo<PERSON>an          \n", "1028      vwap  kuanfu        zhishu  xuj  guo<PERSON>an          \n", "\n", "             sys_type remark1 remark2 remark3  \n", "0     kafang_zongxian                          \n", "1     kafang_zongxian                          \n", "2     kafang_zongxian                          \n", "3     kafang_zongxian                          \n", "4     kafang_zongxian                          \n", "...               ...     ...     ...     ...  \n", "1024  kafang_zongxian                          \n", "1025  kafang_zongxian                          \n", "1026  kafang_zongxian                          \n", "1027  kafang_zongxian                          \n", "1028  kafang_zongxian                          \n", "\n", "[1029 rows x 21 columns]\n"]}], "source": ["new_df=pd.DataFrame()\n", "new_df['date']=df['create_date'].astype(int)\n", "new_df['id']='guojun_ld_'+df['create_date'].apply(int2str)+df['strategy_id'].apply(int2str)\n", "new_df['operation']=df['strategy_dir'].apply(operation_map)\n", "new_df['create_time']=df['create_time'].astype(int)\n", "new_df['start_time']=df['begin_time'].astype(int)\n", "new_df['end_time']=df['end_time'].astype(int)\n", "new_df['symbol']=df['stock_code'].apply(lambda x:str(int(x)).zfill(6))\n", "new_df['quantity']=df['strategy_qty'].astype(int)\n", "new_df['filled_quantity']=df['strike_qty'].astype(int)\n", "new_df['filled_price']=df['strike_amt']/df['strike_qty']\n", "new_df['account_id']=df['fund_account']\n", "new_df['algo_name']='vwap'\n", "new_df['firm']=df['fund_account'].apply(lambda x:account_firm_map[x])\n", "new_df['algo_provider']='zhishu'\n", "new_df['pm']='xuj'\n", "new_df['broker']='guotaijunan'\n", "new_df['params']=\"\"\n", "new_df['sys_type']=\"kafang_zongxian\"\n", "new_df['remark1']=\"\"\n", "new_df['remark2']=\"\"\n", "new_df['remark3']=\"\"\n", "print(new_df)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["upsert dataframe ret:1029\n"]}], "source": ["mysql.upsert_dataframe(mysql.get_connection(\"************\",\"zs_trading_data\",\"root\",\"jtwmy,dt4gx\",engine=True),new_df,\"algo_parentorder\",{})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## luoding guojun T0 parentorder"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Empty DataFrame\n", "Columns: [row_id, strategy_id, create_date, create_time, update_date, update_time, update_times, opor_no, oper_mac, oper_ip_addr, oper_info, oper_way, oper_func_code, fund_account, pass_no, out_acco, exch_no, stock_code, strategy_dir, strategy_qty, strategy_price, price_type, strategy_amt, exch_crncy_type, remark_info, strategy_date, strategy_time, strategy_status, strategy_deal_status, order_qty, valid_order_qty, order_amt, wtdraw_qty, valid_wtdraw_qty, strike_qty, strike_amt, external_no, strategy_oper_way, strategy_price_type, report_no, strategy_excute_style, strategy_exec_kind, target_pov, strategy_exec_broker, begin_time, end_time, min_qty, min_amt, target_strategy_param, target_strategy_type]\n", "Index: []\n", "\n", "[0 rows x 50 columns]\n"]}], "source": ["# df=pd.read_csv(r\"/home/<USER>/py/stk_py/data/raw_data/data/ld_guojun/tb_tdsest_strategyorder_{}.csv\".format(date))\n", "df=pd.read_csv(r\"/home/<USER>/dav/ld_guojun/tb_tdsest_strategyorder_{}.csv\".format(date))\n", "df=df[df['strategy_exec_broker']=='ldsmart02']\n", "print(df)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"ename": "UFuncTypeError", "evalue": "ufunc 'add' did not contain a loop with signature matching types (dtype('<U10'), dtype('int64')) -> None", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mUFuncTypeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[21]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m new_df=pd.DataFrame()\n\u001b[32m      2\u001b[39m new_df[\u001b[33m'\u001b[39m\u001b[33mdate\u001b[39m\u001b[33m'\u001b[39m]=df[\u001b[33m'\u001b[39m\u001b[33mcreate_date\u001b[39m\u001b[33m'\u001b[39m].astype(\u001b[38;5;28mint\u001b[39m)\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m new_df[\u001b[33m'\u001b[39m\u001b[33mid\u001b[39m\u001b[33m'\u001b[39m]=\u001b[33;43m'\u001b[39;49m\u001b[33;43mguojun_ld_\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m+\u001b[49m\u001b[43mdf\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mcreate_date\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m.\u001b[49m\u001b[43mapply\u001b[49m\u001b[43m(\u001b[49m\u001b[43mint2str\u001b[49m\u001b[43m)\u001b[49m+df[\u001b[33m'\u001b[39m\u001b[33mstrategy_id\u001b[39m\u001b[33m'\u001b[39m].apply(int2str)\n\u001b[32m      4\u001b[39m new_df[\u001b[33m'\u001b[39m\u001b[33mcreate_time\u001b[39m\u001b[33m'\u001b[39m]=df[\u001b[33m'\u001b[39m\u001b[33mcreate_time\u001b[39m\u001b[33m'\u001b[39m].astype(\u001b[38;5;28mint\u001b[39m)\n\u001b[32m      5\u001b[39m new_df[\u001b[33m'\u001b[39m\u001b[33mstart_time\u001b[39m\u001b[33m'\u001b[39m]=df[\u001b[33m'\u001b[39m\u001b[33mbegin_time\u001b[39m\u001b[33m'\u001b[39m].astype(\u001b[38;5;28mint\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pandas/core/ops/common.py:76\u001b[39m, in \u001b[36m_unpack_zerodim_and_defer.<locals>.new_method\u001b[39m\u001b[34m(self, other)\u001b[39m\n\u001b[32m     72\u001b[39m             \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mNotImplemented\u001b[39m\n\u001b[32m     74\u001b[39m other = item_from_zerodim(other)\n\u001b[32m---> \u001b[39m\u001b[32m76\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mmethod\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mother\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pandas/core/arraylike.py:190\u001b[39m, in \u001b[36mOpsMixin.__radd__\u001b[39m\u001b[34m(self, other)\u001b[39m\n\u001b[32m    188\u001b[39m \u001b[38;5;129m@unpack_zerodim_and_defer\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m__radd__\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    189\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[34m__radd__\u001b[39m(\u001b[38;5;28mself\u001b[39m, other):\n\u001b[32m--> \u001b[39m\u001b[32m190\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_arith_method\u001b[49m\u001b[43m(\u001b[49m\u001b[43mother\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mroperator\u001b[49m\u001b[43m.\u001b[49m\u001b[43mradd\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pandas/core/series.py:6135\u001b[39m, in \u001b[36mSeries._arith_method\u001b[39m\u001b[34m(self, other, op)\u001b[39m\n\u001b[32m   6133\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[34m_arith_method\u001b[39m(\u001b[38;5;28mself\u001b[39m, other, op):\n\u001b[32m   6134\u001b[39m     \u001b[38;5;28mself\u001b[39m, other = \u001b[38;5;28mself\u001b[39m._align_for_op(other)\n\u001b[32m-> \u001b[39m\u001b[32m6135\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mbase\u001b[49m\u001b[43m.\u001b[49m\u001b[43mIndexOpsMixin\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_arith_method\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mother\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mop\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pandas/core/base.py:1382\u001b[39m, in \u001b[36mIndexOpsMixin._arith_method\u001b[39m\u001b[34m(self, other, op)\u001b[39m\n\u001b[32m   1379\u001b[39m     rvalues = np.arange(rvalues.start, rvalues.stop, rvalues.step)\n\u001b[32m   1381\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m np.errstate(\u001b[38;5;28mall\u001b[39m=\u001b[33m\"\u001b[39m\u001b[33mignore\u001b[39m\u001b[33m\"\u001b[39m):\n\u001b[32m-> \u001b[39m\u001b[32m1382\u001b[39m     result = \u001b[43mops\u001b[49m\u001b[43m.\u001b[49m\u001b[43marithmetic_op\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlvalues\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrvalues\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mop\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1384\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._construct_result(result, name=res_name)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pandas/core/ops/array_ops.py:283\u001b[39m, in \u001b[36marithmetic_op\u001b[39m\u001b[34m(left, right, op)\u001b[39m\n\u001b[32m    279\u001b[39m     _bool_arith_check(op, left, right)  \u001b[38;5;66;03m# type: ignore[arg-type]\u001b[39;00m\n\u001b[32m    281\u001b[39m     \u001b[38;5;66;03m# error: Argument 1 to \"_na_arithmetic_op\" has incompatible type\u001b[39;00m\n\u001b[32m    282\u001b[39m     \u001b[38;5;66;03m# \"Union[ExtensionArray, ndarray[Any, Any]]\"; expected \"ndarray[Any, Any]\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m283\u001b[39m     res_values = \u001b[43m_na_arithmetic_op\u001b[49m\u001b[43m(\u001b[49m\u001b[43mleft\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mright\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mop\u001b[49m\u001b[43m)\u001b[49m  \u001b[38;5;66;03m# type: ignore[arg-type]\u001b[39;00m\n\u001b[32m    285\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m res_values\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pandas/core/ops/array_ops.py:218\u001b[39m, in \u001b[36m_na_arithmetic_op\u001b[39m\u001b[34m(left, right, op, is_cmp)\u001b[39m\n\u001b[32m    215\u001b[39m     func = partial(expressions.evaluate, op)\n\u001b[32m    217\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m218\u001b[39m     result = \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mleft\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mright\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    219\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[32m    220\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m is_cmp \u001b[38;5;129;01mand\u001b[39;00m (\n\u001b[32m    221\u001b[39m         left.dtype == \u001b[38;5;28mobject\u001b[39m \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(right, \u001b[33m\"\u001b[39m\u001b[33mdtype\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m) == \u001b[38;5;28mobject\u001b[39m\n\u001b[32m    222\u001b[39m     ):\n\u001b[32m   (...)\u001b[39m\u001b[32m    225\u001b[39m         \u001b[38;5;66;03m# Don't do this for comparisons, as that will handle complex numbers\u001b[39;00m\n\u001b[32m    226\u001b[39m         \u001b[38;5;66;03m#  incorrectly, see GH#32047\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pandas/core/roperator.py:11\u001b[39m, in \u001b[36mradd\u001b[39m\u001b[34m(left, right)\u001b[39m\n\u001b[32m     10\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[34mradd\u001b[39m(left, right):\n\u001b[32m---> \u001b[39m\u001b[32m11\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mright\u001b[49m\u001b[43m \u001b[49m\u001b[43m+\u001b[49m\u001b[43m \u001b[49m\u001b[43mleft\u001b[49m\n", "\u001b[31mUFuncTypeError\u001b[39m: ufunc 'add' did not contain a loop with signature matching types (dtype('<U10'), dtype('int64')) -> None"]}], "source": ["new_df=pd.DataFrame()\n", "new_df['date']=df['create_date'].astype(int)\n", "new_df['id']='guojun_ld_'+df['create_date'].apply(int2str)+df['strategy_id'].apply(int2str)\n", "new_df['create_time']=df['create_time'].astype(int)\n", "new_df['start_time']=df['begin_time'].astype(int)\n", "new_df['end_time']=df['end_time'].astype(int)\n", "new_df['symbol']=df['stock_code'].apply(lambda x:str(x).zfill(6))\n", "new_df['quantity']=df['strategy_qty'].astype(int)\n", "new_df['account_id']=df['fund_account']\n", "new_df['algo_name']='aaron_t0_v1'\n", "new_df['firm']=df['fund_account'].apply(lambda x:account_firm_map[x])\n", "new_df['algo_provider']='zhishu'\n", "new_df['pm']='aaron'\n", "new_df['broker']='guotaijunan'\n", "new_df['params']=\"\"\n", "new_df['sys_type']=\"kafang_zongxian\"\n", "new_df['remark1']=\"\"\n", "new_df['remark2']=\"\"\n", "new_df['remark3']=\"\"\n", "print(new_df)\n", "mysql.upsert_dataframe(mysql.get_connection(\"************\",\"zs_trading_data\",\"root\",\"jtwmy,dt4gx\",engine=True),new_df,\"intraday_parentorder\",{})"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [date]\n", "Index: []"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["new_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ld guojun orders"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    row_id  order_id  create_date  create_time  update_date  update_time  \\\n", "0  3812565     10000     ********        94000     ********        94000   \n", "1  3812566     10001     ********        94000     ********        94000   \n", "2  3812567     10002     ********        94000     ********        94001   \n", "\n", "   update_times  opor_no  oper_mac  oper_ip_addr  ...  order_frozen_amt  \\\n", "0             4        0       NaN           NaN  ...                 0   \n", "1             3        0       NaN           NaN  ...                 0   \n", "2             3        0       NaN           NaN  ...                 0   \n", "\n", "   order_frozen_qty rsp_info  remark_info  order_oper_way strategy_id  \\\n", "0                 0      NaN                          NaN       10089   \n", "1                 0      NaN                          NaN       10660   \n", "2                 0      NaN                          NaN       10694   \n", "\n", "   external_info  orig_strategy_id       fund_account  strategy_no  \n", "0                                0  9225553_1102_1102         1002  \n", "1                                0  9225553_1102_1102         1002  \n", "2                                0  9225553_1102_1102         1002  \n", "\n", "[3 rows x 45 columns]\n"]}], "source": ["# df=pd.read_csv(r\"/home/<USER>/py/stk_py/data/raw_data/data/ld_guojun/tb_tdsetd_order_{}.csv\".format(date))\n", "df=pd.read_csv(r\"/home/<USER>/dav/ld_guojun/tb_tdsetd_order_{}.csv\".format(date),encoding='gbk')\n", "print(df.head(3))"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["def status_map(s):\n", "    if s=='1':\n", "        return 1\n", "    if s=='2':\n", "        return 2\n", "    if s=='3':\n", "        return 5\n", "    if s=='4':\n", "        return 7\n", "    if s=='5':\n", "        return 3\n", "    if s=='6':\n", "        return 6\n", "    if s=='7':\n", "        return 7\n", "    if s=='8':\n", "        return 9\n", "    return 0"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                        id                      pid   create_time  \\\n", "0  guojun_ld_********10000  guojun_ld_********10089  1.749548e+09   \n", "1  guojun_ld_********10001  guojun_ld_********10660  1.749548e+09   \n", "2  guojun_ld_********10002  guojun_ld_********10694  1.749548e+09   \n", "\n", "   last_upd_time  symbol         account_id  operation  price  quantity  \\\n", "0   1.749548e+09  001311  9225553_1102_1102          1  20.29        30   \n", "1   1.749548e+09  688690  9225553_1102_1102          1  22.90        65   \n", "2   1.749548e+09  688618  9225553_1102_1102          1  22.14       103   \n", "\n", "   filled_price  filled_quantity  status order_id  order_type err_msg remark1  \\\n", "0         20.34               30       6                    0                   \n", "1           NaN                0       9                    0                   \n", "2           NaN                0       9                    0                   \n", "\n", "  remark2  \n", "0          \n", "1          \n", "2          \n"]}], "source": ["new_df=pd.DataFrame()\n", "new_df['id']='guojun_ld_'+df['create_date'].apply(int2str)+df['order_id'].apply(int2str)\n", "new_df['pid']='guojun_ld_'+df['create_date'].apply(int2str)+df['strategy_id'].apply(int2str)\n", "new_df['create_time']=pd.to_datetime(df['create_date'].apply(int2str)+df['create_time'].apply(lambda x:str(int(x)).zfill(6))).astype('int64') // 1e9\n", "new_df['last_upd_time']=pd.to_datetime(df['create_date'].apply(int2str)+df['update_time'].apply(lambda x:str(int(x)).zfill(6))).astype('int64') // 1e9\n", "new_df['symbol']=df['stock_code'].apply(lambda x:str(x).zfill(6))\n", "new_df['account_id']=df['fund_account']\n", "new_df['operation']=df['order_dir'].apply(operation_map)\n", "new_df['price']=df['order_price']\n", "new_df['quantity']=df['order_qty'].astype(int)\n", "new_df['filled_price']=df['strike_amt']/df['strike_qty']\n", "new_df['filled_quantity']=df['strike_qty'].astype(int)\n", "new_df['status']=df['order_status'].astype(str).apply(status_map)\n", "new_df['order_id']=''\n", "new_df['order_type']=0\n", "new_df['err_msg']=''\n", "new_df['remark1']=''\n", "new_df['remark2']=''\n", "print(new_df.head(3))"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["upsert dataframe ret:7407\n"]}], "source": ["mysql.upsert_dataframe(mysql.get_connection(\"************\",\"zs_trading_data\",\"root\",\"jtwmy,dt4gx\",engine=True),new_df,\"orders\",{})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 中泰数据"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["# date=\"20250509\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 拆单parentorder"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   start_time  end_time\n", "0       93000    100000\n", "1       93000    100000\n", "2       93000    100000\n", "3       93000    100000\n", "4       93000    100000\n", "(322, 21)\n"]}], "source": ["# pos=pd.read_csv(r'/home/<USER>/py/stk_py/data/raw_data/data/zhongtai_tuoguan/parentorder_{}.csv'.format(date))\n", "# basket=pd.read_csv(r'/home/<USER>/py/stk_py/data/raw_data/data/zhongtai_tuoguan/basket_{}.csv'.format(date))\n", "pos=pd.read_csv(r'/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/{}/parentorder_{}.csv'.format(date, date))\n", "basket=pd.read_csv(r'/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/{}/basket_{}.csv'.format(date, date))\n", "basket=basket.rename(columns={basket.columns[0]:'basket_id','开始时间':'start_time','结束时间':'end_time'})\n", "basket=basket[['basket_id','start_time','end_time']]\n", "pos=pos.rename(columns={pos.columns[0]:'symbol','母单编号':'basket_id','成交数量':'filled_quantity','成交金额':'filled_amt','剩余数量':'rem_qty','方向':'side'})\n", "pos=pos[['basket_id','symbol','side','filled_quantity','filled_amt','rem_qty']]\n", "pos=pos.merge(basket,how='left',on='basket_id')\n", "pos['filled_amt']=pos['filled_amt'].str.replace(',','').astype(float)\n", "pos['filled_quantity']=pos['filled_quantity'].str.replace(',','').astype(float)\n", "pos['symbol']=pos['symbol'].apply(lambda x:str(x).z<PERSON>(6))\n", "pos['side']=pos['side'].apply(lambda x:0 if x=='买' else 1)\n", "pos['quantity']=pos['filled_quantity']+pos['rem_qty']\n", "pos['filled_price']=pos['filled_amt']/pos['filled_quantity']\n", "pos['start_time']=pos['start_time'].str.replace(':',\"\")+'00'\n", "pos['end_time']=pos['end_time'].str.replace(':',\"\")+'00'\n", "pos['create_time']=pos['start_time']    \n", "new_df=pd.DataFrame()\n", "new_df['id']=date+pos['basket_id'].apply(int2str)+pos['symbol']\n", "new_df['operation']=pos['side']\n", "new_df['create_time']=pos['create_time'].astype(int)\n", "new_df['start_time']=pos['start_time'].astype(int)\n", "new_df['end_time']=pos['end_time'].astype(int)\n", "new_df['symbol']=pos['symbol']\n", "new_df['quantity']=pos['quantity'].astype(int)\n", "new_df['filled_quantity']=pos['filled_quantity'].astype(int)  \n", "new_df['filled_price']=pos['filled_price'].astype(float)\n", "new_df['account_id']='clz_zhongtai_109156033251'\n", "new_df['algo_name']='vwap'\n", "new_df['firm']='chaoliangzi'\n", "new_df['algo_provider']='haoxing'\n", "new_df['pm']=''\n", "new_df['broker']='zhongtai'\n", "new_df['params']=\"\"\n", "new_df['sys_type']=\"xtp\"\n", "new_df['remark1']=\"\"\n", "new_df['remark2']=\"\"\n", "new_df['remark3']=\"\"\n", "new_df['date']=date\n", "print(new_df[['start_time', 'end_time']].head())\n", "print(new_df.shape)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["upsert dataframe ret:322\n"]}], "source": ["mysql.upsert_dataframe(mysql.get_connection(\"************\",\"zs_trading_data\",\"root\",\"jtwmy,dt4gx\",engine=True),new_df,\"algo_parentorder\",{})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 拆单子单"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["def status_map(s):\n", "    if s==' 全部成交':\n", "        return 6\n", "    if s==' 已撤单':\n", "        return 8\n", "    if s==' 部分撤单':\n", "        return 8\n", "    if s==' 已拒绝':\n", "        return 9\n", "    return 0"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["# df=pd.read_csv(r'/home/<USER>/py/stk_py/data/raw_data/data/zhongtai_tuoguan/order_{}.csv'.format(date))\n", "df=pd.read_csv(r'/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/{}/order_{}.csv'.format(date,date))\n", "df=df.rename(columns={df.columns[0]:'basket_id','证券代码':'symbol','方向':'side','报单状态':'status','委托价格':'price','委托数量':'quantity','成交数量':'filled_quantity','成交金额':'filled_amt','剩余数量':'rem_qty','委托时间':'time'})\n", "df=df[['basket_id','symbol','side','status','price','quantity','filled_quantity','filled_amt','rem_qty','time','rowId']]\n", "df['quantity']=df['quantity'].str.replace(',','').astype(float)\n", "df['filled_quantity']=df['filled_quantity'].str.replace(',','').astype(float)\n", "df['filled_amt']=df['filled_amt'].str.replace(',','').astype(float)\n", "df['side']=df['side'].apply(lambda x:0 if x=='买' else 1)\n", "df['symbol']=df['symbol'].apply(lambda x:str(x).z<PERSON>(6))\n", "new_df=pd.DataFrame()\n", "new_df['id']=df['rowId']\n", "new_df['pid']=date+df['basket_id'].apply(int2str)+df['symbol']\n", "new_df['create_time']=pd.to_datetime(date+\" \"+df['time']).astype('int64') // 1e9\n", "new_df['last_upd_time']=new_df['create_time']\n", "new_df['symbol']=df['symbol']\n", "new_df['account_id']='clz_zhongtai_109156033251'\n", "new_df['operation']=df['side']\n", "new_df['price']=df['price']\n", "new_df['quantity']=df['quantity'].astype(int)\n", "new_df['filled_price']=(df['filled_amt']/df['filled_quantity']).fillna(0)\n", "new_df['filled_quantity']=df['filled_quantity'].astype(int)\n", "new_df['status']=df['status'].astype(str).apply(status_map)\n", "new_df['order_id']=''\n", "new_df['order_type']=0\n", "new_df['err_msg']=''\n", "new_df['remark1']=''\n", "new_df['remark2']=''"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["upsert dataframe ret:576\n"]}], "source": ["mysql.upsert_dataframe(mysql.get_connection(\"************\",\"zs_trading_data\",\"root\",\"jtwmy,dt4gx\",engine=True),new_df,\"orders\",{})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 日内母单"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["# pos=pd.read_csv(r'/home/<USER>/py/stk_py/data/raw_data/data/zhongtai_tuoguan/t0_parentorder_{}.csv'.format(date))\n", "# basket=pd.read_csv(r'/home/<USER>/py/stk_py/data/raw_data/data/zhongtai_tuoguan/t0_basket_{}.csv'.format(date))\n", "pos=pd.read_csv(r'/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/{}/t0_parentorder_{}.csv'.format(date,date))\n", "basket=pd.read_csv(r'/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/{}/t0_basket_{}.csv'.format(date,date))"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["basket=basket.rename(columns={basket.columns[0]:'basket_id','开始时间':'start_time','结束时间':'end_time'})\n", "basket=basket[['basket_id','start_time','end_time']]"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["pos=pos.rename(columns={pos.columns[0]:'symbol','母单编号':'basket_id','标的数量':'quantity'})\n", "pos=pos[['basket_id','symbol','quantity']]\n", "pos=pos.merge(basket,how='left',on='basket_id')"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["pos['quantity']=pos['quantity'].str.replace(',','').astype(float)\n", "pos['symbol']=pos['symbol'].apply(lambda x:str(x).z<PERSON>(6))\n", "pos['start_time']=pos['start_time'].str.replace(':',\"\")+'00'\n", "pos['end_time']=pos['end_time'].str.replace(':',\"\")+'00'\n", "pos['create_time']=pos['start_time']    "]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["new_df=pd.DataFrame()\n", "new_df['id']=date+pos['basket_id'].apply(int2str)+pos['symbol']\n", "new_df['create_time']=pos['create_time'].astype(int)\n", "new_df['start_time']=pos['start_time'].astype(int)\n", "new_df['end_time']=pos['end_time'].astype(int)\n", "new_df['symbol']=pos['symbol']\n", "new_df['quantity']=pos['quantity'].astype(int)\n", "new_df['account_id']='clz_zhongtai_109156033251'\n", "new_df['algo_name']='yueran_t0'\n", "new_df['firm']='chaoliangzi'\n", "new_df['algo_provider']='yueran'\n", "new_df['pm']=''\n", "new_df['broker']='zhongtai'\n", "new_df['params']=\"\"\n", "new_df['sys_type']=\"XTP\"\n", "new_df['remark1']=\"\"\n", "new_df['remark2']=\"\"\n", "new_df['remark3']=\"\"\n", "new_df['date']=date"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["upsert dataframe ret:406\n"]}], "source": ["mysql.upsert_dataframe(mysql.get_connection(\"************\",\"zs_trading_data\",\"root\",\"jtwmy,dt4gx\",engine=True),new_df,\"intraday_parentorder\",{})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 日内子单"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["def status_map(s):\n", "    if s==' 全部成交':\n", "        return 6\n", "    if s==' 已撤单':\n", "        return 8\n", "    if s==' 部分撤单':\n", "        return 8\n", "    if s==' 已拒绝':\n", "        return 9\n", "    return 0"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["# df=pd.read_csv(r'/home/<USER>/py/stk_py/data/raw_data/data/zhongtai_tuoguan/t0_order_{}.csv'.format(date))\n", "df=pd.read_csv(r'/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/{}/t0_order_{}.csv'.format(date,date))"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["df=df.rename(columns={df.columns[0]:'basket_id','证券代码':'symbol','方向':'side','报单状态':'status','委托价格':'price','委托数量':'quantity','成交数量':'filled_quantity','成交金额':'filled_amt','剩余数量':'rem_qty','委托时间':'time'})\n", "df=df[['basket_id','symbol','side','status','price','quantity','filled_quantity','filled_amt','rem_qty','time','rowId']]\n", "df['quantity']=df['quantity'].str.replace(',','').astype(float)\n", "df['filled_quantity']=df['filled_quantity'].str.replace(',','').astype(float)\n", "df['filled_amt']=df['filled_amt'].str.replace(',','').astype(float)\n", "df['side']=df['side'].apply(lambda x:0 if x=='买' else 1)\n", "df['symbol']=df['symbol'].apply(lambda x:str(x).z<PERSON>(6))"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["new_df=pd.DataFrame()\n", "new_df['id']=df['rowId']\n", "new_df['pid']=date+df['basket_id'].apply(int2str)+df['symbol']\n", "new_df['create_time']=pd.to_datetime(date+\" \"+df['time']).astype('int64') // 1e9\n", "new_df['last_upd_time']=new_df['create_time']\n", "new_df['symbol']=df['symbol']\n", "new_df['account_id']='clz_zhongtai_109156033251'\n", "new_df['operation']=df['side']\n", "new_df['price']=df['price']\n", "new_df['quantity']=df['quantity'].astype(int)\n", "new_df['filled_price']=(df['filled_amt']/df['filled_quantity']).fillna(0)\n", "new_df['filled_quantity']=df['filled_quantity'].astype(int)\n", "new_df['status']=df['status'].astype(str).apply(status_map)\n", "new_df['order_id']=''\n", "new_df['order_type']=0\n", "new_df['err_msg']=''\n", "new_df['remark1']=''\n", "new_df['remark2']=''"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["upsert dataframe ret:978\n"]}], "source": ["mysql.upsert_dataframe(mysql.get_connection(\"************\",\"zs_trading_data\",\"root\",\"jtwmy,dt4gx\",engine=True),new_df,\"orders\",{})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "#==================================================================================="]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df=pd.read_csv(r\"/home/<USER>/py/stk_py/data/raw_data/data/ld_guojun/tb_tdsest_strategyorder.csv\")\n", "df=pd.read_csv(r\"/home/<USER>/py/stk_py/data/raw_data/data/ld_guojun/tb_tdsest_strategyorder.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['ldsmart01', 'ldsmart02'], dtype=object)"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df['fund_account']==''].unique()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['28131077_3104_3104', '218321_3302_3302', nan], dtype=object)"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df['strategy_exec_broker']=='ldsmart02']['fund_account'].unique()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from data import data_reader"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["select TICKER_SYMBOL,CLOSE_PRICE from datayes.mkt_equd_adj where TRADE_DATE='********'\n"]}], "source": ["pxs=data_reader.get_close_pxs_by_date_from_mysql(\"********\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'000001': 11.43,\n", " '000002': 6.89,\n", " '000004': 8.48,\n", " '000006': 6.6,\n", " '000007': 7.28,\n", " '000008': 2.87,\n", " '000009': 8.14,\n", " '000010': 2.85,\n", " '000011': 8.43,\n", " '000012': 4.82,\n", " '000014': 10.86,\n", " '000016': 5.33,\n", " '000017': 6.45,\n", " '000019': 6.75,\n", " '000020': 11.9,\n", " '000021': 18.34,\n", " '000025': 17.13,\n", " '000026': 11.64,\n", " '000027': 6.52,\n", " '000028': 25.12,\n", " '000029': 17.18,\n", " '000030': 6.13,\n", " '000031': 2.64,\n", " '000032': 22.38,\n", " '000034': 40.74,\n", " '000035': 4.47,\n", " '000036': 3.59,\n", " '000037': 8.48,\n", " '000039': 8.35,\n", " '000042': 6.75,\n", " '000045': 10.3,\n", " '000048': 17.25,\n", " '000049': 21.97,\n", " '000050': 8.41,\n", " '000055': 4.13,\n", " '000056': 3.19,\n", " '000058': 8.24,\n", " '000059': 5.33,\n", " '000060': 4.63,\n", " '000061': 6.62,\n", " '000062': 27.95,\n", " '000063': 33.0,\n", " '000065': 11.48,\n", " '000066': 14.97,\n", " '000068': 3.36,\n", " '000069': 2.4,\n", " '000070': 6.4,\n", " '000078': 2.35,\n", " '000088': 4.89,\n", " '000089': 7.02,\n", " '000090': 3.6,\n", " '000096': 16.92,\n", " '000099': 23.2,\n", " '000100': 4.33,\n", " '000151': 14.34,\n", " '000153': 5.92,\n", " '000155': 10.39,\n", " '000156': 7.8,\n", " '000157': 7.6,\n", " '000158': 25.9,\n", " '000159': 5.42,\n", " '000166': 4.92,\n", " '000301': 9.37,\n", " '000333': 77.09,\n", " '000338': 15.29,\n", " '000400': 22.03,\n", " '000401': 4.73,\n", " '000402': 2.82,\n", " '000403': 21.62,\n", " '000404': 7.25,\n", " '000407': 3.32,\n", " '000408': 36.84,\n", " '000409': 11.65,\n", " '000410': 6.95,\n", " '000411': 10.6,\n", " '000415': 3.34,\n", " '000417': 5.76,\n", " '000419': 5.44,\n", " '000420': 4.1,\n", " '000421': 6.13,\n", " '000422': 12.69,\n", " '000423': 55.8,\n", " '000425': 8.73,\n", " '000426': 13.96,\n", " '000428': 3.18,\n", " '000429': 14.17,\n", " '000430': 7.7,\n", " '000488': 1.88,\n", " '000498': 5.84,\n", " '000501': 9.24,\n", " '000503': 10.64,\n", " '000504': 6.77,\n", " '000505': 6.41,\n", " '000506': 7.42,\n", " '000507': 5.89,\n", " '000509': 3.16,\n", " '000510': 5.41,\n", " '000513': 35.2,\n", " '000514': 4.54,\n", " '000516': 5.27,\n", " '000517': 1.91,\n", " '000518': 2.07,\n", " '000519': 17.42,\n", " '000520': 5.6,\n", " '000521': 7.78,\n", " '000523': 3.3,\n", " '000524': 12.07,\n", " '000525': 6.89,\n", " '000526': 52.9,\n", " '000528': 10.18,\n", " '000529': 6.05,\n", " '000530': 5.51,\n", " '000531': 6.28,\n", " '000532': 14.94,\n", " '000533': 5.95,\n", " '000534': 15.3,\n", " '000536': 4.58,\n", " '000537': 8.58,\n", " '000538': 57.16,\n", " '000539': 4.38,\n", " '000541': 6.58,\n", " '000543': 7.48,\n", " '000544': 8.27,\n", " '000545': 2.34,\n", " '000546': 4.4,\n", " '000547': 7.4,\n", " '000548': 5.14,\n", " '000550': 19.71,\n", " '000551': 12.4,\n", " '000552': 2.44,\n", " '000553': 6.67,\n", " '000554': 6.33,\n", " '000555': 11.31,\n", " '000557': 5.39,\n", " '000558': 3.9,\n", " '000559': 9.0,\n", " '000560': 3.23,\n", " '000561': 7.99,\n", " '000563': 3.48,\n", " '000564': 2.59,\n", " '000565': 11.58,\n", " '000566': 4.94,\n", " '000567': 5.8,\n", " '000568': 130.14,\n", " '000570': 5.56,\n", " '000571': 4.66,\n", " '000572': 3.85,\n", " '000573': 4.4,\n", " '000576': 10.07,\n", " '000581': 20.95,\n", " '000582': 9.19,\n", " '000584': 1.46,\n", " '000586': 10.13,\n", " '000589': 4.6,\n", " '000590': 8.99,\n", " '000591': 4.45,\n", " '000592': 3.09,\n", " '000593': 5.64,\n", " '000595': 4.35,\n", " '000596': 168.75,\n", " '000597': 5.17,\n", " '000598': 7.21,\n", " '000599': 4.81,\n", " '000600': 6.7,\n", " '000601': 5.94,\n", " '000603': 13.51,\n", " '000605': 7.58,\n", " '000607': 3.82,\n", " '000608': 1.67,\n", " '000609': 2.73,\n", " '000610': 13.02,\n", " '000612': 7.1,\n", " '000615': 3.29,\n", " '000617': 7.29,\n", " '000619': 5.95,\n", " '000620': 1.71,\n", " '000622': 1.78,\n", " '000623': 16.84,\n", " '000625': 12.45,\n", " '000626': 6.27,\n", " '000627': 2.74,\n", " '000628': 46.58,\n", " '000629': 2.67,\n", " '000630': 3.27,\n", " '000631': 2.9,\n", " '000632': 3.91,\n", " '000633': 5.59,\n", " '000635': 8.24,\n", " '000636': 13.66,\n", " '000637': 4.03,\n", " '000638': 3.74,\n", " '000639': 2.88,\n", " '000650': 5.44,\n", " '000651': 46.9,\n", " '000652': 3.89,\n", " '000655': 6.81,\n", " '000656': 1.6,\n", " '000657': 10.64,\n", " '000659': 3.28,\n", " '000661': 97.66,\n", " '000663': 5.79,\n", " '000665': 5.19,\n", " '000668': 5.83,\n", " '000669': 2.02,\n", " '000670': 6.91,\n", " '000672': 8.47,\n", " '000676': 8.35,\n", " '000677': 5.11,\n", " '000678': 18.15,\n", " '000679': 6.37,\n", " '000680': 9.15,\n", " '000681': 21.42,\n", " '000682': 9.68,\n", " '000683': 5.25,\n", " '000685': 8.82,\n", " '000686': 7.63,\n", " '000688': 11.85,\n", " '000690': 4.37,\n", " '000691': 4.34,\n", " '000692': 3.41,\n", " '000695': 9.4,\n", " '000697': 6.83,\n", " '000698': 3.72,\n", " '000700': 7.24,\n", " '000701': 5.2,\n", " '000702': 6.64,\n", " '000703': 6.09,\n", " '000705': 7.98,\n", " '000707': 6.36,\n", " '000708': 11.53,\n", " '000709': 2.23,\n", " '000710': 14.35,\n", " '000711': 1.85,\n", " '000712': 13.79,\n", " '000713': 7.15,\n", " '000715': 7.21,\n", " '000716': 6.41,\n", " '000717': 2.64,\n", " '000718': 2.16,\n", " '000719': 12.44,\n", " '000720': 3.16,\n", " '000721': 9.03,\n", " '000722': 11.98,\n", " '000723': 4.27,\n", " '000725': 3.95,\n", " '000726': 6.36,\n", " '000727': 2.5,\n", " '000728': 7.85,\n", " '000729': 12.71,\n", " '000731': 6.88,\n", " '000733': 48.43,\n", " '000735': 6.02,\n", " '000736': 4.27,\n", " '000737': 9.48,\n", " '000738': 19.31,\n", " '000739': 13.95,\n", " '000750': 3.97,\n", " '000751': 2.96,\n", " '000752': 8.66,\n", " '000753': 5.26,\n", " '000755': 5.09,\n", " '000756': 14.0,\n", " '000757': 4.54,\n", " '000758': 4.91,\n", " '000759': 8.52,\n", " '000761': 3.97,\n", " '000762': 19.22,\n", " '000766': 19.65,\n", " '000767': 3.35,\n", " '000768': 24.81,\n", " '000776': 17.31,\n", " '000777': 17.62,\n", " '000778': 3.51,\n", " '000779': 9.58,\n", " '000782': 5.1,\n", " '000783': 6.84,\n", " '000785': 3.44,\n", " '000786': 29.21,\n", " '000788': 5.72,\n", " '000789': 5.45,\n", " '000790': 3.7,\n", " '000791': 6.68,\n", " '000792': 16.25,\n", " '000793': 2.72,\n", " '000795': 10.93,\n", " '000796': 3.76,\n", " '000797': 2.74,\n", " '000798': 7.1,\n", " '000799': 45.14,\n", " '000800': 7.28,\n", " '000801': 16.78,\n", " '000802': 4.41,\n", " '000803': 5.48,\n", " '000807': 15.95,\n", " '000809': 3.2,\n", " '000810': 11.79,\n", " '000811': 13.18,\n", " '000812': 4.49,\n", " '000813': 3.12,\n", " '000815': 14.2,\n", " '000816': 3.16,\n", " '000818': 25.04,\n", " '000819': 14.69,\n", " '000820': 2.35,\n", " '000821': 11.43,\n", " '000822': 5.55,\n", " '000823': 10.16,\n", " '000825': 3.62,\n", " '000826': 1.76,\n", " '000828': 10.52,\n", " '000829': 10.16,\n", " '000830': 10.96,\n", " '000831': 33.32,\n", " '000833': 12.7,\n", " '000837': 13.23,\n", " '000838': 2.47,\n", " '000839': 2.68,\n", " '000848': 9.54,\n", " '000850': 4.24,\n", " '000851': 2.36,\n", " '000852': 6.3,\n", " '000856': 11.4,\n", " '000858': 135.26,\n", " '000859': 7.17,\n", " '000860': 16.12,\n", " '000862': 5.68,\n", " '000863': 3.93,\n", " '000868': 5.79,\n", " '000869': 22.73,\n", " '000875': 5.43,\n", " '000876': 9.75,\n", " '000877': 4.94,\n", " '000878': 12.1,\n", " '000880': 36.72,\n", " '000881': 7.2,\n", " '000882': 2.23,\n", " '000883': 4.67,\n", " '000885': 13.9,\n", " '000886': 6.16,\n", " '000887': 19.16,\n", " '000888': 13.39,\n", " '000889': 3.03,\n", " '000890': 3.6,\n", " '000892': 3.59,\n", " '000893': 28.38,\n", " '000895': 24.77,\n", " '000897': 2.29,\n", " '000898': 2.41,\n", " '000899': 9.46,\n", " '000900': 4.11,\n", " '000901': 11.34,\n", " '000902': 13.76,\n", " '000903': 3.59,\n", " '000905': 8.5,\n", " '000906': 6.6,\n", " '000908': 4.81,\n", " '000909': 5.71,\n", " '000910': 7.6,\n", " '000911': 7.41,\n", " '000912': 4.91,\n", " '000913': 15.93,\n", " '000915': 30.47,\n", " '000917': 7.36,\n", " '000919': 6.58,\n", " '000920': 9.98,\n", " '000921': 29.3,\n", " '000922': 10.74,\n", " '000923': 13.48,\n", " '000925': 7.57,\n", " '000926': 2.22,\n", " '000927': 2.63,\n", " '000928': 6.72,\n", " '000929': 7.26,\n", " '000930': 5.53,\n", " '000931': 4.96,\n", " '000932': 4.97,\n", " '000933': 17.8,\n", " '000935': 16.41,\n", " '000936': 7.03,\n", " '000937': 6.76,\n", " '000938': 25.54,\n", " '000948': 19.1,\n", " '000949': 4.15,\n", " '000950': 4.94,\n", " '000951': 17.57,\n", " '000952': 5.75,\n", " '000953': 6.31,\n", " '000955': 4.44,\n", " '000957': 11.38,\n", " '000958': 7.57,\n", " '000959': 3.65,\n", " '000960': 14.49,\n", " '000962': 16.13,\n", " '000963': 39.24,\n", " '000965': 4.06,\n", " '000966': 5.0,\n", " '000967': 6.62,\n", " '000968': 6.69,\n", " '000969': 12.19,\n", " '000970': 11.76,\n", " '000972': 3.3,\n", " '000973': 6.32,\n", " '000975': 19.58,\n", " '000977': 51.85,\n", " '000978': 6.01,\n", " '000980': 2.21,\n", " '000981': 1.96,\n", " '000983': 6.52,\n", " '000985': 17.17,\n", " '000987': 6.75,\n", " '000988': 43.2,\n", " '000989': 8.64,\n", " '000990': 7.73,\n", " '000993': 10.71,\n", " '000995': 13.4,\n", " '000997': 30.35,\n", " '000998': 10.26,\n", " '000999': 42.11,\n", " '001201': 14.77,\n", " '001202': 16.88,\n", " '001203': 8.74,\n", " '001205': 17.98,\n", " '001206': 19.01,\n", " '001207': 21.08,\n", " '001208': 10.48,\n", " '001209': 17.6,\n", " '001210': 15.29,\n", " '001211': 22.05,\n", " '001212': 66.57,\n", " '001213': 4.13,\n", " '001215': 27.03,\n", " '001216': 13.49,\n", " '001217': 11.35,\n", " '001218': 17.08,\n", " '001219': 13.57,\n", " '001222': 16.06,\n", " '001223': 51.7,\n", " '001225': 34.2,\n", " '001226': 27.04,\n", " '001227': 2.41,\n", " '001228': 23.81,\n", " '001229': 31.87,\n", " '001230': 18.57,\n", " '001231': 18.62,\n", " '001234': 20.2,\n", " '001236': 9.99,\n", " '001238': 35.7,\n", " '001239': 15.11,\n", " '001255': 32.64,\n", " '001256': 22.93,\n", " '001258': 7.05,\n", " '001259': 22.94,\n", " '001260': 17.76,\n", " '001266': 24.42,\n", " '001267': 8.28,\n", " '001268': 22.61,\n", " '001269': 24.48,\n", " '001270': 36.35,\n", " '001277': 37.62,\n", " '001278': 16.5,\n", " '001279': 40.15,\n", " '001282': 27.82,\n", " '001283': 54.2,\n", " '001286': 9.1,\n", " '001287': 19.4,\n", " '001288': 30.1,\n", " '001289': 17.06,\n", " '001296': 17.25,\n", " '001298': 27.78,\n", " '001299': 10.75,\n", " '001300': 13.32,\n", " '001301': 52.2,\n", " '001306': 83.74,\n", " '001308': 21.97,\n", " '001309': 127.38,\n", " '001311': 28.23,\n", " '001313': 8.66,\n", " '001314': 44.96,\n", " '001316': 49.83,\n", " '001317': 40.92,\n", " '001318': 12.93,\n", " '001319': 27.35,\n", " '001322': 8.58,\n", " '001323': 31.77,\n", " '001324': 18.23,\n", " '001326': 33.71,\n", " '001328': 50.5,\n", " '001330': 4.48,\n", " '001331': 13.22,\n", " '001332': 35.27,\n", " '001333': 21.79,\n", " '001335': 38.27,\n", " '001336': 19.88,\n", " '001337': 24.06,\n", " '001338': 11.33,\n", " '001339': 52.79,\n", " '001356': 15.95,\n", " '001358': 23.78,\n", " '001359': 31.96,\n", " '001360': 13.34,\n", " '001366': 11.24,\n", " '001367': 29.83,\n", " '001368': 22.14,\n", " '001373': 29.04,\n", " '001376': 12.76,\n", " '001378': 21.72,\n", " '001379': 20.36,\n", " '001380': 33.58,\n", " '001382': 22.54,\n", " '001387': 12.99,\n", " '001389': 50.23,\n", " '001391': 7.49,\n", " '001395': 49.75,\n", " '001400': 58.08,\n", " '001696': 23.73,\n", " '001872': 20.15,\n", " '001896': 4.72,\n", " '001914': 11.74,\n", " '001965': 13.0,\n", " '001979': 9.07,\n", " '002001': 22.68,\n", " '002003': 11.65,\n", " '002004': 4.11,\n", " '002005': 1.74,\n", " '002006': 18.24,\n", " '002007': 16.31,\n", " '002008': 24.55,\n", " '002009': 16.34,\n", " '002010': 5.39,\n", " '002011': 11.77,\n", " '002012': 4.82,\n", " '002014': 11.1,\n", " '002015': 7.33,\n", " '002016': 5.63,\n", " '002017': 10.03,\n", " '002019': 12.07,\n", " '002020': 13.37,\n", " '002021': 2.3,\n", " '002022': 6.0,\n", " '002023': 9.89,\n", " '002024': 1.9,\n", " '002025': 49.88,\n", " '002026': 11.06,\n", " '002027': 7.48,\n", " '002028': 74.0,\n", " '002029': 6.52,\n", " '002030': 5.96,\n", " '002031': 9.22,\n", " '002032': 56.36,\n", " '002033': 8.86,\n", " '002034': 17.72,\n", " '002035': 6.65,\n", " '002036': 10.65,\n", " '002037': 8.47,\n", " '002038': 6.58,\n", " '002039': 16.26,\n", " '002040': 8.45,\n", " '002041': 10.19,\n", " '002042': 5.26,\n", " '002043': 10.31,\n", " '002044': 5.67,\n", " '002045': 15.13,\n", " '002046': 16.04,\n", " '002047': 1.89,\n", " '002048': 16.46,\n", " '002049': 66.15,\n", " '002050': 27.56,\n", " '002051': 8.5,\n", " '002052': 9.02,\n", " '002053': 11.29,\n", " '002054': 6.6,\n", " '002055': 5.95,\n", " '002056': 13.97,\n", " '002057': 8.17,\n", " '002058': 10.16,\n", " '002059': 5.11,\n", " '002060': 3.54,\n", " '002061': 3.92,\n", " '002062': 4.87,\n", " '002063': 5.87,\n", " '002064': 7.28,\n", " '002065': 10.34,\n", " '002066': 10.96,\n", " '002067': 3.61,\n", " '002068': 11.42,\n", " '002069': 3.87,\n", " '002072': 4.66,\n", " '002073': 8.17,\n", " '002074': 23.83,\n", " '002075': 6.05,\n", " '002076': 1.95,\n", " '002077': 14.16,\n", " '002078': 14.09,\n", " '002079': 9.65,\n", " '002080': 14.95,\n", " '002081': 3.59,\n", " '002082': 5.7,\n", " '002083': 4.84,\n", " '002084': 3.53,\n", " '002085': 17.0,\n", " '002086': 2.8,\n", " '002088': 11.87,\n", " '002090': 9.03,\n", " '002091': 7.5,\n", " '002092': 5.03,\n", " '002093': 12.29,\n", " '002094': 8.97,\n", " '002095': 22.19,\n", " '002096': 12.14,\n", " '002097': 7.36,\n", " '002098': 10.42,\n", " '002099': 5.2,\n", " '002100': 6.36,\n", " '002101': 12.91,\n", " '002102': 2.83,\n", " '002103': 9.5,\n", " '002104': 8.08,\n", " '002105': 7.83,\n", " '002106': 11.17,\n", " '002107': 5.21,\n", " '002108': 3.74,\n", " '002109': 3.4,\n", " '002110': 3.89,\n", " '002111': 10.18,\n", " '002112': 13.71,\n", " '002114': 6.39,\n", " '002115': 7.98,\n", " '002116': 9.96,\n", " '002117': 11.63,\n", " '002119': 15.7,\n", " '002120': 6.92,\n", " '002121': 4.69,\n", " '002122': 4.28,\n", " '002123': 14.36,\n", " '002124': 3.16,\n", " '002125': 10.22,\n", " '002126': 26.43,\n", " '002127': 4.49,\n", " '002128': 17.85,\n", " '002129': 8.16,\n", " '002130': 21.14,\n", " '002131': 3.88,\n", " '002132': 3.37,\n", " '002133': 2.87,\n", " '002134': 18.43,\n", " '002135': 4.29,\n", " '002136': 10.33,\n", " '002137': 7.82,\n", " '002138': 27.9,\n", " '002139': 14.37,\n", " '002140': 9.87,\n", " '002141': 3.02,\n", " '002142': 26.32,\n", " '002144': 11.86,\n", " '002145': 4.35,\n", " '002146': 1.45,\n", " '002148': 6.55,\n", " '002149': 19.48,\n", " '002150': 11.33,\n", " '002151': 26.77,\n", " '002152': 12.45,\n", " '002153': 8.05,\n", " '002154': 3.94,\n", " '002155': 22.63,\n", " '002156': 25.94,\n", " '002157': 2.81,\n", " '002158': 19.08,\n", " '002159': 14.65,\n", " '002160': 3.83,\n", " '002161': 5.47,\n", " '002162': 4.23,\n", " '002163': 8.62,\n", " '002164': 10.69,\n", " '002165': 12.28,\n", " '002166': 7.54,\n", " '002167': 9.28,\n", " '002168': 2.77,\n", " '002169': 6.24,\n", " '002170': 10.49,\n", " '002171': 8.68,\n", " '002172': 3.65,\n", " '002173': 10.18,\n", " '002174': 9.92,\n", " '002175': 4.65,\n", " '002176': 7.24,\n", " '002177': 4.74,\n", " '002178': 6.18,\n", " '002179': 40.46,\n", " '002180': 22.4,\n", " '002181': 5.55,\n", " '002182': 12.39,\n", " '002183': 5.12,\n", " '002184': 12.2,\n", " '002185': 9.44,\n", " '002186': 10.49,\n", " '002187': 7.06,\n", " '002188': 5.08,\n", " '002189': 19.53,\n", " '002190': 29.66,\n", " '002191': 3.72,\n", " '002192': 29.37,\n", " '002193': 5.66,\n", " '002194': 14.73,\n", " '002195': 5.86,\n", " '002196': 10.55,\n", " '002197': 5.3,\n", " '002198': 6.87,\n", " '002199': 5.24,\n", " '002200': 8.25,\n", " '002201': 6.55,\n", " '002202': 9.23,\n", " '002203': 10.12,\n", " '002204': 6.29,\n", " '002205': 9.68,\n", " '002206': 5.13,\n", " '002207': 5.74,\n", " '002208': 6.57,\n", " '002209': 12.05,\n", " '002210': 2.56,\n", " '002211': 2.69,\n", " '002212': 7.48,\n", " '002213': 14.46,\n", " '002214': 9.17,\n", " '002215': 9.36,\n", " '002216': 11.53,\n", " '002217': 2.16,\n", " '002218': 3.2,\n", " '002219': 2.43,\n", " '002221': 8.37,\n", " '002222': 34.25,\n", " '002223': 35.8,\n", " '002224': 4.55,\n", " '002225': 5.58,\n", " '002226': 5.49,\n", " '002227': 11.75,\n", " '002228': 3.32,\n", " '002229': 15.56,\n", " '002230': 47.76,\n", " '002231': 2.9,\n", " '002232': 17.46,\n", " '002233': 7.64,\n", " '002234': 8.34,\n", " '002235': 5.34,\n", " '002236': 15.9,\n", " '002237': 10.91,\n", " '002238': 8.2,\n", " '002239': 2.95,\n", " '002240': 11.94,\n", " '002241': 22.69,\n", " '002242': 9.77,\n", " '002243': 8.14,\n", " '002244': 9.68,\n", " '002245': 13.33,\n", " '002246': 11.34,\n", " '002247': 2.6,\n", " '002248': 8.58,\n", " '002249': 7.05,\n", " '002250': 6.54,\n", " '002251': 5.5,\n", " '002252': 6.8,\n", " '002253': 8.21,\n", " '002254': 10.5,\n", " '002255': 9.37,\n", " '002256': 2.26,\n", " '002258': 10.39,\n", " '002259': 4.01,\n", " '002261': 34.18,\n", " '002262': 21.64,\n", " '002263': 2.61,\n", " '002264': 7.04,\n", " '002265': 23.17,\n", " '002266': 3.29,\n", " '002267': 9.3,\n", " '002268': 15.8,\n", " '002269': 1.83,\n", " '002270': 15.41,\n", " '002271': 11.42,\n", " '002272': 11.08,\n", " '002273': 20.49,\n", " '002274': 7.26,\n", " '002275': 13.91,\n", " '002276': 15.8,\n", " '002277': 5.35,\n", " '002278': 8.59,\n", " '002279': 6.73,\n", " '002281': 45.38,\n", " '002282': 7.43,\n", " '002283': 6.6,\n", " '002284': 11.6,\n", " '002285': 2.33,\n", " '002286': 9.35,\n", " '002287': 24.86,\n", " '002289': 12.68,\n", " '002290': 32.56,\n", " '002291': 6.11,\n", " '002292': 9.1,\n", " '002293': 9.0,\n", " '002294': 38.3,\n", " '002295': 7.67,\n", " '002296': 11.26,\n", " '002297': 7.33,\n", " '002298': 5.92,\n", " '002299': 15.62,\n", " '002300': 6.01,\n", " '002301': 7.08,\n", " '002302': 5.95,\n", " '002303': 3.57,\n", " '002304': 69.73,\n", " '002305': 1.74,\n", " '002306': 1.73,\n", " '002307': 4.28,\n", " '002309': 2.64,\n", " '002310': 2.14,\n", " '002311': 57.3,\n", " '002312': 11.47,\n", " '002313': 9.05,\n", " '002314': 2.53,\n", " '002315': 43.68,\n", " '002316': 3.84,\n", " '002317': 12.3,\n", " '002318': 24.09,\n", " '002319': 10.09,\n", " '002320': 6.13,\n", " '002321': 2.42,\n", " '002322': 12.68,\n", " '002323': 1.54,\n", " '002324': 10.49,\n", " '002326': 11.21,\n", " '002327': 8.03,\n", " '002328': 5.64,\n", " '002329': 3.38,\n", " '002330': 4.39,\n", " '002331': 9.19,\n", " '002332': 9.32,\n", " '002333': 5.3,\n", " '002334': 8.05,\n", " '002335': 44.4,\n", " '002336': 3.69,\n", " '002337': 5.49,\n", " '002338': 45.91,\n", " '002339': 7.09,\n", " '002340': 6.33,\n", " '002342': 5.51,\n", " '002343': 7.08,\n", " '002344': 4.27,\n", " '002345': 9.51,\n", " '002346': 13.92,\n", " '002347': 7.49,\n", " '002348': 3.51,\n", " '002349': 7.07,\n", " '002350': 6.58,\n", " '002351': 13.45,\n", " '002352': 43.99,\n", " '002353': 34.19,\n", " '002354': 6.82,\n", " '002355': 6.85,\n", " '002356': 3.0,\n", " '002357': 7.79,\n", " '002358': 5.26,\n", " '002360': 4.94,\n", " '002361': 6.19,\n", " '002362': 22.24,\n", " '002363': 7.29,\n", " '002364': 16.1,\n", " '002365': 12.41,\n", " '002366': 5.15,\n", " '002367': 7.05,\n", " '002368': 24.71,\n", " '002369': 9.5,\n", " '002370': 3.27,\n", " '002371': 436.94,\n", " '002372': 11.82,\n", " '002373': 9.45,\n", " '002374': 2.66,\n", " '002375': 3.83,\n", " '002376': 6.9,\n", " '002377': 2.89,\n", " '002378': 7.77,\n", " '002379': 10.22,\n", " '002380': 24.73,\n", " '002381': 6.64,\n", " '002382': 5.26,\n", " '002383': 9.63,\n", " '002384': 28.45,\n", " '002385': 4.11,\n", " '002386': 4.64,\n", " '002387': 9.18,\n", " '002388': 3.79,\n", " '002389': 20.48,\n", " '002390': 3.57,\n", " '002391': 5.51,\n", " '002392': 6.26,\n", " '002393': 16.67,\n", " '002394': 8.17,\n", " '002395': 16.99,\n", " '002396': 19.99,\n", " '002397': 3.85,\n", " '002398': 4.83,\n", " '002399': 11.11,\n", " '002400': 7.81,\n", " '002401': 15.79,\n", " '002402': 20.47,\n", " '002403': 14.57,\n", " '002404': 6.74,\n", " '002405': 8.48,\n", " '002406': 7.75,\n", " '002407': 12.01,\n", " '002408': 4.68,\n", " '002409': 56.57,\n", " '002410': 14.73,\n", " '002412': 5.75,\n", " '002413': 5.13,\n", " '002414': 8.54,\n", " '002415': 29.01,\n", " '002416': 11.98,\n", " '002418': 3.41,\n", " '002419': 5.92,\n", " '002420': 6.04,\n", " '002421': 3.59,\n", " '002422': 33.89,\n", " '002423': 12.33,\n", " '002424': 3.96,\n", " '002425': 2.4,\n", " '002426': 2.91,\n", " '002427': 7.81,\n", " '002428': 19.4,\n", " '002429': 4.55,\n", " '002430': 20.37,\n", " '002431': 1.99,\n", " '002432': 38.78,\n", " '002434': 7.96,\n", " '002436': 12.72,\n", " '002437': 2.45,\n", " '002438': 11.08,\n", " '002439': 15.84,\n", " '002440': 7.62,\n", " '002441': 8.72,\n", " '002442': 6.26,\n", " '002443': 6.1,\n", " '002444': 25.26,\n", " '002445': 2.3,\n", " '002446': 6.75,\n", " '002448': 8.02,\n", " '002449': 8.85,\n", " '002451': 6.6,\n", " '002452': 7.16,\n", " '002453': 5.64,\n", " '002454': 7.34,\n", " '002455': 7.59,\n", " '002456': 11.96,\n", " '002457': 11.58,\n", " '002458': 8.35,\n", " '002459': 9.99,\n", " '002460': 32.15,\n", " '002461': 10.71,\n", " '002462': 13.14,\n", " '002463': 31.64,\n", " '002465': 10.84,\n", " '002466': 30.23,\n", " '002467': 5.46,\n", " '002468': 10.72,\n", " '002469': 9.13,\n", " '002470': 1.72,\n", " '002471': 3.94,\n", " '002472': 35.09,\n", " '002474': 6.68,\n", " '002475': 33.52,\n", " '002476': 4.41,\n", " '002478': 5.33,\n", " '002479': 5.22,\n", " '002480': 5.59,\n", " '002481': 5.21,\n", " '002482': 1.84,\n", " '002483': 6.37,\n", " '002484': 19.2,\n", " '002485': 3.06,\n", " '002486': 2.39,\n", " '002487': 28.43,\n", " '002488': 14.17,\n", " '002489': 3.7,\n", " '002490': 4.39,\n", " '002491': 5.13,\n", " '002492': 6.16,\n", " '002493': 8.79,\n", " '002494': 4.67,\n", " '002495': 2.62,\n", " '002496': 1.44,\n", " '002497': 11.45,\n", " '002498': 3.36,\n", " '002500': 5.87,\n", " '002501': 2.13,\n", " '002506': 2.38,\n", " '002507': 13.26,\n", " '002508': 20.19,\n", " '002510': 5.87,\n", " '002511': 6.83,\n", " '002512': 4.74,\n", " '002513': 4.73,\n", " '002514': 6.51,\n", " '002515': 5.34,\n", " ...}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["pxs"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['row_id', 'strategy_id', 'create_date', 'create_time', 'update_date',\n", "       'update_time', 'update_times', 'opor_no', 'oper_mac', 'oper_ip_addr',\n", "       'oper_info', 'oper_way', 'oper_func_code', 'fund_account', 'pass_no',\n", "       'out_acco', 'exch_no', 'stock_code', 'strategy_dir', 'strategy_qty',\n", "       'strategy_price', 'price_type', 'strategy_amt', 'exch_crncy_type',\n", "       'remark_info', 'strategy_date', 'strategy_time', 'strategy_status',\n", "       'strategy_deal_status', 'order_qty', 'valid_order_qty', 'order_amt',\n", "       'wtdraw_qty', 'valid_wtdraw_qty', 'strike_qty', 'strike_amt',\n", "       'external_no', 'strategy_oper_way', 'strategy_price_type', 'report_no',\n", "       'strategy_excute_style', 'strategy_exec_kind', 'target_pov',\n", "       'strategy_exec_broker', 'begin_time', 'end_time', 'min_qty', 'min_amt',\n", "       'target_strategy_param', 'target_strategy_type'],\n", "      dtype='object')"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["row_id                                        1406731\n", "strategy_id                                     10000\n", "create_date                                  ********\n", "create_time                                    124013\n", "update_date                                  ********\n", "update_time                                    145500\n", "update_times                                        4\n", "opor_no                                             0\n", "oper_mac                                          NaN\n", "oper_ip_addr                                      NaN\n", "oper_info                                         NaN\n", "oper_way                                          NaN\n", "oper_func_code                                    NaN\n", "fund_account                        9225553_1102_1102\n", "pass_no                                             0\n", "out_acco                            9225553_1102_1102\n", "exch_no                                             2\n", "stock_code                                     300906\n", "strategy_dir                                        2\n", "strategy_qty                                      100\n", "strategy_price                                    0.0\n", "price_type                                          1\n", "strategy_amt                                        0\n", "exch_crncy_type                                   NaN\n", "remark_info                                       NaN\n", "strategy_date                                ********\n", "strategy_time                                  124013\n", "strategy_status                                     9\n", "strategy_deal_status                                1\n", "order_qty                                           0\n", "valid_order_qty                                     0\n", "order_amt                                           0\n", "wtdraw_qty                                          0\n", "valid_wtdraw_qty                                    0\n", "strike_qty                                          0\n", "strike_amt                                          0\n", "external_no                       6881693728131709400\n", "strategy_oper_way                                 NaN\n", "strategy_price_type                                 0\n", "report_no                                         NaN\n", "strategy_excute_style                             NaN\n", "strategy_exec_kind                                  0\n", "target_pov                                        0.1\n", "strategy_exec_broker                        ldsmart01\n", "begin_time                                     130000\n", "end_time                                       145500\n", "min_qty                                             0\n", "min_amt                                             0\n", "target_strategy_param    version:0;must_complete:true\n", "target_strategy_type                             VWAP\n", "Name: 0, dtype: object"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["df.il<PERSON>[0]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["df=df[df['target_strategy_type']=='VWAP']"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1624/3902869384.py:1: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df['px']=df['stock_code'].apply(lambda x:str(x).zfill(6)).apply(lambda x:pxs[x])\n"]}], "source": ["df['px']=df['stock_code'].apply(lambda x:str(x).zfill(6)).apply(lambda x:pxs[x])"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1624/1465985328.py:1: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df['amt']=df['strategy_qty']*df['px']\n"]}], "source": ["df['amt']=df['strategy_qty']*df['px']"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["********.02"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["df['amt'].sum()"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["fund_account\n", "218321_3302_3302      3872030.00\n", "28131077_3104_3104    3361548.86\n", "9225553_1102_1102     6488116.16\n", "Name: amt, dtype: float64"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["df.groupby('fund_account')['amt'].sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# 讯投数据"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 母单数据"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [], "source": ["# import leveldb\n", "import plyvel\n", "import datetime\n", "import json\n", "import pandas as pd\n", "from utils import mysql\n", "import os\n", "# def read_algo_data(p):\n", "#     # ldb=leveldb.LevelDB(p)\n", "#     ldb = plyvel.DB(p)\n", "#     l=[]\n", "#     datas=list(ldb.RangeIter())\n", "#     # true='true'\n", "#     pos=[]\n", "#     for k,v in datas:\n", "#         d=json.loads(v)\n", "#         pos.append(d) \n", "#     po_df=pd.DataFrame(pos)\n", "#     ldb=None\n", "#     return po_df \n", "\n", "def read_algo_data(p):\n", "    # 1. 打开数据库\n", "    db = plyvel.DB(p, create_if_missing=False)\n", "\n", "    # 2. 获取迭代器对象\n", "    iterator = db.iterator()  # 创建迭代器\n", "\n", "    # 3. 遍历所有键值对（方法1：手动遍历迭代器）\n", "    pos = []\n", "    for k, v in iterator:  # 直接遍历迭代器（默认从第一个键开始）\n", "        d = json.loads(v.decode('utf-8'))  # 解码 bytes 并解析 JSON\n", "        pos.append(d)\n", "\n", "    # 4. 关闭数据库\n", "    db.close()\n", "\n", "    # 5. 返回 DataFrame\n", "    return pd.DataFrame(pos)\n", "\n", "def operation_map(x):\n", "    if x==100:\n", "        return 0\n", "    if x==110:\n", "        return 1\n", "    return None\n", "\n", "def status_map(s):\n", "    if s==0:\n", "        return 1\n", "    if s==10:\n", "        return 2\n", "    if s==20:\n", "        return 2\n", "    if s==30:\n", "        return 3\n", "    if s==40:\n", "        return 5\n", "    if s==100:\n", "        return 6\n", "    if s==110:\n", "        return 7\n", "    if s==120:\n", "        return 9\n", "    return 0"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                        id  symbol  quantity  order_type  \\\n", "0     0000072000002135_1013_13_339@GRZQQMT  000027      1300           1   \n", "1     0000072000002135_1014_14_340@GRZQQMT  000050       600           1   \n", "2     0000072000002135_1015_15_341@GRZQQMT  000068       100           1   \n", "3     0000072000002135_1016_16_342@GRZQQMT  000099       700           1   \n", "4     0000072000002135_1017_17_343@GRZQQMT  000159       900           1   \n", "...                                    ...     ...       ...         ...   \n", "1808   00008883558888_1008_8_907@guojinQMT  003015       100           1   \n", "1809   00008883558888_1008_8_908@guojinQMT  002100       300           1   \n", "1810   00008883558888_1008_8_909@guojinQMT  600770       400           1   \n", "1811   00008883558888_1008_8_910@guojinQMT  300041       200           1   \n", "1812   00008883558888_1008_8_911@guojinQMT  002996       200           1   \n", "\n", "      operation  status    create_time  last_upd_time     start_time  \\\n", "0           100     130  1749531731000  1749533400027  1749531731000   \n", "1           110     130  1749531731000  1749533400028  1749531731000   \n", "2           100     130  1749531731000  1749533400013  1749531731000   \n", "3           100     130  1749531731000  1749533400029  1749531731000   \n", "4           100     130  1749531731000  1749533400011  1749531731000   \n", "...         ...     ...            ...            ...            ...   \n", "1808        110     100  1749517658000  1749520442183  1749519600000   \n", "1809        110     100  1749517658000  1749519629868  1749519600000   \n", "1810        110     100  1749517658000  1749519870278  1749519600000   \n", "1811        110     100  1749517658000  1749519737560  1749519600000   \n", "1812        110     100  1749517658000  1749520876103  1749519600000   \n", "\n", "           end_time algo_name  \\\n", "0     1749533400000   ZS_VWAP   \n", "1     1749533400000   ZS_VWAP   \n", "2     1749533400000   ZS_VWAP   \n", "3     1749533400000   ZS_VWAP   \n", "4     1749533400000   ZS_VWAP   \n", "...             ...       ...   \n", "1808  1749526199000   ZS_VWAP   \n", "1809  1749526199000   ZS_VWAP   \n", "1810  1749526199000   ZS_VWAP   \n", "1811  1749526199000   ZS_VWAP   \n", "1812  1749526199000   ZS_VWAP   \n", "\n", "                                                 params  mkt  \\\n", "0     {\"min_qty\":0,\"min_amt\":0,\"version\":5,\"must_com...    2   \n", "1     {\"min_qty\":0,\"min_amt\":0,\"version\":5,\"must_com...    2   \n", "2     {\"min_qty\":0,\"min_amt\":0,\"version\":5,\"must_com...    2   \n", "3     {\"min_qty\":0,\"min_amt\":0,\"version\":5,\"must_com...    2   \n", "4     {\"min_qty\":0,\"min_amt\":0,\"version\":5,\"must_com...    2   \n", "...                                                 ...  ...   \n", "1808  {\"min_qty\":0,\"min_amt\":0,\"version\":5,\"must_com...    2   \n", "1809  {\"min_qty\":0,\"min_amt\":0,\"version\":5,\"must_com...    2   \n", "1810  {\"min_qty\":0,\"min_amt\":0,\"version\":5,\"must_com...    1   \n", "1811  {\"min_qty\":0,\"min_amt\":0,\"version\":5,\"must_com...    2   \n", "1812  {\"min_qty\":0,\"min_amt\":0,\"version\":5,\"must_com...    2   \n", "\n", "                                       account_id  filled_quantity  \\\n", "0     2____10313____111____49____072000002135____              NaN   \n", "1     2____10313____111____49____072000002135____              NaN   \n", "2     2____10313____111____49____072000002135____              NaN   \n", "3     2____10313____111____49____072000002135____              NaN   \n", "4     2____10313____111____49____072000002135____              NaN   \n", "...                                           ...              ...   \n", "1808  2____10355____10355____49____8883558888____            100.0   \n", "1809  2____10355____10355____49____8883558888____            300.0   \n", "1810  2____10355____10355____49____8883558888____            400.0   \n", "1811  2____10355____10355____49____8883558888____            200.0   \n", "1812  2____10355____10355____49____8883558888____            200.0   \n", "\n", "      filled_price  \n", "0              NaN  \n", "1              NaN  \n", "2              NaN  \n", "3              NaN  \n", "4              NaN  \n", "...            ...  \n", "1808         14.06  \n", "1809          6.29  \n", "1810          5.67  \n", "1811          9.01  \n", "1812          7.20  \n", "\n", "[1813 rows x 16 columns]\n"]}], "source": ["# date=\"20250530\"\n", "date=datetime.datetime.now().strftime('%Y%m%d')\n", "pt=r'/data/shared-data/public/algo_trading_data/xuntou/parentorder_{}.ldb'.format(date)\n", "df=read_algo_data(pt)\n", "print(df)"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["df['create_time']=(pd.to_datetime(df['create_time'],unit='ms')+pd.Timedelta(hours=8)).apply(lambda datetime_val:datetime_val.hour * 10000 + datetime_val.minute * 100 + datetime_val.second)\n", "df['start_time']=(pd.to_datetime(df['start_time'],unit='ms')+pd.Timedelta(hours=8)).apply(lambda datetime_val:datetime_val.hour * 10000 + datetime_val.minute * 100 + datetime_val.second)\n", "df['end_time']=(pd.to_datetime(df['end_time'],unit='ms')+pd.Timedelta(hours=8)).apply(lambda datetime_val:datetime_val.hour * 10000 + datetime_val.minute * 100 + datetime_val.second)\n", "df['operation']=df['operation'].apply(operation_map)\n", "df['status']=df['status'].apply(status_map)\n", "df['firm']='unknown'\n", "df['algo_provider']='zhishu'\n", "df['pm']='xj'\n", "df['date']=date\n", "df['broker']='unknown'\n", "df['sys_type']='xuntou'\n", "df['remark1']=''\n", "df['remark2']=''\n", "df['remark3']=''\n", "df=df[['id','symbol','quantity','operation','create_time','start_time','end_time','filled_quantity','filled_price','algo_name','account_id','firm','algo_provider','pm','date','broker','params','sys_type','remark1','remark2','remark3']]"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["delete failed...'Engine' object has no attribute 'execute'\n", "upsert dataframe ret:1813\n"]}], "source": ["mysql.upsert_dataframe(mysql.get_connection(\"************\",\"zs_trading_data\",\"root\",\"jtwmy,dt4gx\",engine=True),df,\"algo_parentorder\",{'date':date,'sys_type':'xuntou'})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 子单"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                        id  \\\n", "0     001524b9-0567-4ec2-b155-b97b9676525a   \n", "1     0023cf94-1941-43bf-8c87-9c9f45436153   \n", "2     002f2ff9-91df-45c1-9e24-4a14420fe1f1   \n", "3     0038df56-abc6-4954-b5dc-a840d3230374   \n", "4     003f49e2-c980-4bc1-b97d-852c8d9b3d5f   \n", "...                                    ...   \n", "2990  ffd3c4d9-c8bb-4587-a4a9-a855b2e9acc4   \n", "2991  ffdafc14-fe31-4ead-94be-2fe6713e78aa   \n", "2992  ffdb3c4d-b981-4dc3-bbbd-9a1f7ef8d50c   \n", "2993  ffe1c17f-92f5-4a0e-a760-34878bd5c9c2   \n", "2994  fff2e706-3425-4ab1-ae95-f4b84791e792   \n", "\n", "                                         pid    create_time  last_upd_time  \\\n", "0     0000072000002135_1824_824_1150@GRZQQMT  1749536347583  1749536347600   \n", "1        00008883558888_1008_8_789@guojinQMT  1749521635597  1749521650607   \n", "2        00008883558888_1008_8_557@guojinQMT  1749519738197  1749519738235   \n", "3      0000072000002135_1650_650_976@GRZQQMT  1749535812371  1749535815555   \n", "4      0000072000002135_1517_517_843@GRZQQMT  1749531744579  1749531744601   \n", "...                                      ...            ...            ...   \n", "2990     00008883558888_1007_7_327@guojinQMT  *************  *************   \n", "2991     00008883558888_1008_8_478@guojinQMT  *************  *************   \n", "2992   0000072000002135_1536_536_862@GRZQQMT  *************  *************   \n", "2993   0000072000002135_1436_436_762@GRZQQMT  *************  *************   \n", "2994     00008883558888_1008_8_508@guojinQMT  *************  *************   \n", "\n", "      symbol                                   account_id  price  quantity  \\\n", "0     301386  2____10313____111____49____072000002135____  21.05       100   \n", "1     301367  2____10355____10355____49____8883558888____  86.40       100   \n", "2     688289  2____10355____10355____49____8883558888____  20.29       200   \n", "3     002221  2____10313____111____49____072000002135____   8.84       300   \n", "4     601696  2____10313____111____49____072000002135____  10.16       200   \n", "...      ...                                          ...    ...       ...   \n", "2990  002206  2____10355____10355____49____8883558888____   5.10       400   \n", "2991  603296  2____10355____10355____49____8883558888____  71.50       100   \n", "2992  603083  2____10313____111____49____072000002135____  44.02       100   \n", "2993  600489  2____10313____111____49____072000002135____  14.10       200   \n", "2994  000713  2____10355____10355____49____8883558888____   7.05       100   \n", "\n", "      filled_price  filled_quantity  order_type  operation  status  \\\n", "0            21.05            100.0           1        110     100   \n", "1              NaN              NaN           1        110     110   \n", "2            20.29            200.0           1        110     100   \n", "3             8.84            300.0           1        110     100   \n", "4            10.16            200.0           1        100     100   \n", "...            ...              ...         ...        ...     ...   \n", "2990          5.10            400.0           1        100     100   \n", "2991           NaN              NaN           1        110     110   \n", "2992         44.02            100.0           1        100     100   \n", "2993           NaN              NaN           1        100     110   \n", "2994           NaN              NaN           1        110     110   \n", "\n", "        order_id                    remark  market  \n", "0          65237                    v5#3_0       2  \n", "1     15091669_1                    v5#2_0       2  \n", "2     15044318_1                    v5#3_0       1  \n", "3          62983   v5#3_0.2899385338759576       2  \n", "4          42182  v5#3_0.*****************       1  \n", "...          ...                       ...     ...  \n", "2990  15043410_1                    v5#3_0       2  \n", "2991  15086156_1            pass_-0.000026       1  \n", "2992       42611             pass_0.001103       1  \n", "2993       50621             pass_0.000159       1  \n", "2994  15047228_1                    v5#3_0       2  \n", "\n", "[2995 rows x 16 columns]\n", "Index(['id', 'pid', 'create_time', 'last_upd_time', 'symbol', 'account_id',\n", "       'price', 'quantity', 'filled_price', 'filled_quantity', 'order_type',\n", "       'operation', 'status', 'order_id', 'remark', 'market'],\n", "      dtype='object')\n"]}], "source": ["pt=r'/data/shared-data/public/algo_trading_data/xuntou/order_{}.ldb'.format(date)\n", "df=read_algo_data(pt)\n", "print(df)\n", "print(df.columns)"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                        id  \\\n", "0     001524b9-0567-4ec2-b155-b97b9676525a   \n", "1     0023cf94-1941-43bf-8c87-9c9f45436153   \n", "2     002f2ff9-91df-45c1-9e24-4a14420fe1f1   \n", "3     0038df56-abc6-4954-b5dc-a840d3230374   \n", "4     003f49e2-c980-4bc1-b97d-852c8d9b3d5f   \n", "...                                    ...   \n", "2990  ffd3c4d9-c8bb-4587-a4a9-a855b2e9acc4   \n", "2991  ffdafc14-fe31-4ead-94be-2fe6713e78aa   \n", "2992  ffdb3c4d-b981-4dc3-bbbd-9a1f7ef8d50c   \n", "2993  ffe1c17f-92f5-4a0e-a760-34878bd5c9c2   \n", "2994  fff2e706-3425-4ab1-ae95-f4b84791e792   \n", "\n", "                                         pid  symbol  create_time  \\\n", "0     0000072000002135_1824_824_1150@GRZQQMT  301386       141907   \n", "1        00008883558888_1008_8_789@guojinQMT  301367       101355   \n", "2        00008883558888_1008_8_557@guojinQMT  688289        94218   \n", "3      0000072000002135_1650_650_976@GRZQQMT  002221       141012   \n", "4      0000072000002135_1517_517_843@GRZQQMT  601696       130224   \n", "...                                      ...     ...          ...   \n", "2990     00008883558888_1007_7_327@guojinQMT  002206        94157   \n", "2991     00008883558888_1008_8_478@guojinQMT  603296       100828   \n", "2992   0000072000002135_1536_536_862@GRZQQMT  603083       130320   \n", "2993   0000072000002135_1436_436_762@GRZQQMT  600489       132305   \n", "2994     00008883558888_1008_8_508@guojinQMT  000713        94330   \n", "\n", "      last_upd_time                                   account_id  operation  \\\n", "0            141907  2____10313____111____49____072000002135____          1   \n", "1            101410  2____10355____10355____49____8883558888____          1   \n", "2             94218  2____10355____10355____49____8883558888____          1   \n", "3            141015  2____10313____111____49____072000002135____          1   \n", "4            130224  2____10313____111____49____072000002135____          0   \n", "...             ...                                          ...        ...   \n", "2990          94431  2____10355____10355____49____8883558888____          0   \n", "2991         100922  2____10355____10355____49____8883558888____          1   \n", "2992         130320  2____10313____111____49____072000002135____          0   \n", "2993         132320  2____10313____111____49____072000002135____          0   \n", "2994          95533  2____10355____10355____49____8883558888____          1   \n", "\n", "      price  quantity  filled_price  filled_quantity  status    order_id  \\\n", "0     21.05       100         21.05            100.0       6       65237   \n", "1     86.40       100           NaN              NaN       7  15091669_1   \n", "2     20.29       200         20.29            200.0       6  15044318_1   \n", "3      8.84       300          8.84            300.0       6       62983   \n", "4     10.16       200         10.16            200.0       6       42182   \n", "...     ...       ...           ...              ...     ...         ...   \n", "2990   5.10       400          5.10            400.0       6  15043410_1   \n", "2991  71.50       100           NaN              NaN       7  15086156_1   \n", "2992  44.02       100         44.02            100.0       6       42611   \n", "2993  14.10       200           NaN              NaN       7       50621   \n", "2994   7.05       100           NaN              NaN       7  15047228_1   \n", "\n", "      order_type err_msg remark1 remark2  \n", "0              1                          \n", "1              1                          \n", "2              1                          \n", "3              1                          \n", "4              1                          \n", "...          ...     ...     ...     ...  \n", "2990           1                          \n", "2991           1                          \n", "2992           1                          \n", "2993           1                          \n", "2994           1                          \n", "\n", "[2995 rows x 17 columns]\n"]}], "source": ["df['create_time']=(pd.to_datetime(df['create_time'],unit='ms')+pd.Timedelta(hours=8)).apply(lambda datetime_val:datetime_val.hour * 10000 + datetime_val.minute * 100 + datetime_val.second)\n", "df['last_upd_time']=(pd.to_datetime(df['last_upd_time'],unit='ms')+pd.Timedelta(hours=8)).apply(lambda datetime_val:datetime_val.hour * 10000 + datetime_val.minute * 100 + datetime_val.second)\n", "df['operation']=df['operation'].apply(operation_map)\n", "df['status']=df['status'].apply(status_map)\n", "df['firm']='unknown'\n", "df['algo_provider']='zhishu'\n", "df['pm']='xj'\n", "df['date']=date\n", "df['broker']='unknown'\n", "df['sys_type']='xuntou'\n", "df['remark1']=''\n", "df['remark2']=''\n", "# df['err_msg']=df['error_msg']\n", "df['err_msg'] = ''\n", "\n", "df=df[['id','pid','symbol','create_time','last_upd_time','account_id','operation','price','quantity','filled_price','filled_quantity','status','order_id','order_type','err_msg','remark1','remark2']]\n", "print(df)"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["upsert dataframe ret:2995\n"]}], "source": ["mysql.upsert_dataframe(mysql.get_connection(\"************\",\"zs_trading_data\",\"root\",\"jtwmy,dt4gx\",engine=True),df,\"orders\",{})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}