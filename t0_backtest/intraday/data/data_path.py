import os
aaron_signal_root_path="/data/shared-data/xuj/signals/arron_signals"
def file_aaron_signal_bt_daily_sym(date,sym):
    pt="{}/signals/{}/{}".format(aaron_signal_root_path,date,sym)
    return pt

def file_aaron_signal_prd_daily_sym(date,sym):
    pt="{}/prd_signals/{}/{}".format(aaron_signal_root_path,date,sym)
    return pt

def file_jeffrey_signal_bt_daily_sym(date,sym):
    dir = "/data/shared-data/xuj/signals/jeffrey_signals/signals"
    pt="{}/{}/{}".format(dir,date,sym)
    return pt

def dir_aaron_raw_signal_prd_daily(date):
    pt="{}/raw_data_prd/{}".format(aaron_signal_root_path,date)
    return pt

def dir_aaron_raw_signal_bt_daily(date):
    pt="{}/raw_data_bt/{}".format(aaron_signal_root_path,date)
    return pt

def dir_L1_raw_data():
    return "/data/mkt-data/raw_data/L1/"

def dir_L2_raw_data():
    return "/data/mkt-data/raw_data/L2/"

def dir_L2_raw_data_daily(date):
    return "/data/mkt-data/raw_data/L2/"+date+"/"

def dir_stk_1min():
    return dir_L1_raw_data()+"min/"

def dir_processed_snapshot():
    return "/data/shared-data/public/snapshot/"

def dir_processed_snapshot_sym_daily(date):
    return "/data/shared-data/public/snapshot/syms/"+date+"/"


def file_min_daily(date):
    return dir_stk_1min()+"equity_pricemin{}.zip".format(date)

def dir_stk_5min():
    return dir_L1_raw_data()+"05min/"

def file_5min_daily(date):
    return dir_stk_5min()+"equity_price05min{}.zip".format(date)

def file_stk_price_daily(date):
    return "/data/shared-data/xuj/datas/stk_price/"+date

def file_processed_snapshot_daily(date):
    return dir_processed_snapshot()+date

def file_orderbook_stats_daily(date):
    return "/data/shared-data/public/orderbook_datas/orderbook_stats_daily/feather/"+date

def file_processed_snapshot_sym_daily(date,sym):
    return dir_processed_snapshot_sym_daily(date)+sym