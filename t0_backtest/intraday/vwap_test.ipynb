{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from utils import mysql\n", "import numpy as np\n", "import pandas as pd\n", "from utils import mysql\n", "from data import data_reader\n", "import datetime\n", "from joblib import Parallel, delayed, Memory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fmt_td(delta):\n", "    total_seconds = int(delta.total_seconds())\n", "    # 分解为小时、分钟、秒\n", "    hours, remainder = divmod(total_seconds, 3600)\n", "    minutes, seconds = divmod(remainder, 60)\n", "    formatted_time = f\"{hours:02d}:{minutes:02d}:{seconds:02d}\"\n", "    return formatted_time\n", "def stats(datas):\n", "    d={}\n", "    d['executed_value']=datas['executed_notional'].sum()\n", "    d['no_of_porders']=len(datas)\n", "    d['po_amt_mean']=(datas['vwap_full']*datas['quantity']).mean()\n", "    d['po_amt_med']=(datas['vwap_full']*datas['quantity']).median()\n", "    d['vwap']=datas['vwap_full'].mean()\n", "    d['vwap_cost']=-np.average(datas['vwap_cost'],weights=datas['executed_notional']/d['executed_value'])\n", "    d['vwap_costf50']=-np.average(datas['vwap_cost_f50'],weights=datas['executed_notional']/d['executed_value'])\n", "    d['vwap_costl50']=-np.average(datas['vwap_cost_l50'],weights=datas['executed_notional']/d['executed_value'])\n", "    return d\n", "\n", "def show_by_name(datas,groups,stats_func):\n", "    l=[]\n", "    for gn in groups:\n", "        if gn=='total':\n", "            r=stats_func(datas)\n", "            r['group']=gn\n", "            l.append(r)\n", "        else: \n", "            for n,g in datas.groupby(gn):\n", "                r=stats_func(g)\n", "                r['group']=n\n", "                l.append(r)\n", "    return pd.DataFrame(l)\n", "\n", "def show_by_multi_name(datas,grp,stats_func):\n", "    l=[]   \n", "    for names,g in datas.groupby(grp):\n", "        r=stats_func(g)\n", "        for i,n in enumerate(names):\n", "            r['group_{}'.format(i)]=n\n", "        l.append(r)\n", "    return pd.DataFrame(l)\n", "\n", "def analysis(po):\n", "    tks=pd.read_feather(r\"/data/shared-data/public/snapshot/syms/{}/{}\".format(po['date'],po['symbol']))\n", "    d={}\n", "    starttime=po['st']\n", "    endtime=po['et']\n", "    middletime=po['mt']\n", "    # print(starttime,endtime)\n", "    interval_tks=tks[(tks['time']>=starttime)&(tks['time']<=endtime)]\n", "    vwap_full=(interval_tks.iloc[-1]['Turnover']-interval_tks.iloc[0]['Turnover'])/(interval_tks.iloc[-1]['TradVolume']-interval_tks.iloc[0]['TradVolume'])\n", "    interval_tks=tks[(tks['time']>=starttime)&(tks['time']<=middletime)]\n", "    first50_vwap=(interval_tks.iloc[-1]['Turnover']-interval_tks.iloc[0]['Turnover'])/(interval_tks.iloc[-1]['TradVolume']-interval_tks.iloc[0]['TradVolume'])\n", "    interval_tks=tks[(tks['time']>=middletime)&(tks['time']<=endtime)]\n", "    last50_vwap=(interval_tks.iloc[-1]['Turnover']-interval_tks.iloc[0]['Turnover'])/(interval_tks.iloc[-1]['TradVolume']-interval_tks.iloc[0]['TradVolume'])\n", "    side=-1 if po['operation']==1 else 1\n", "    d['id']=po['id']\n", "    d['vwap_full']=vwap_full\n", "    d['first50_vwap']=first50_vwap\n", "    d['last50_vwap']=last50_vwap\n", "    d['side']=side\n", "    d.update(po)\n", "    return d\n", "def calc_cost(avg_px,bm_px,side):\n", "    return  side*(avg_px/bm_px -1 )*10000"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 可以考虑缓存已加载的数据\n", "# from functools import lru_cache\n", "\n", "# memory = Memory(location='./cachedir', verbose=0)\n", "\n", "# @memory.cache\n", "def load_tks(date, symbol):\n", "    return pd.read_feather(f\"/data/shared-data/public/snapshot/syms/{date}/{symbol}\")\n", "\n", "\n", "# 更精确的VWAP计算函数\n", "def calculate_vwap(df):\n", "    df = df.sort_values('time')\n", "    if len(df) < 2:\n", "        return 0\n", "    try:\n", "        vwap = (df['Turnover'].iloc[-1] - df['Turnover'].iloc[0]) / (df['TradVolume'].iloc[-1] - df['TradVolume'].iloc[0])\n", "    except:\n", "        # print(f\"calculate_vwap error: {df}\")\n", "        print(f\"calculate_vwap error: {df['Turnover'].iloc[-1]} - {df['Turnover'].iloc[0]} = {df['TradVolume'].iloc[-1]} - {df['TradVolume'].iloc[0]}\")\n", "        return 0\n", "    return vwap\n", "    \n", "    # df['cum_turnover'] = df['Turnover'].cumsum()\n", "    # df['cum_volume'] = df['TradVolume'].cumsum()\n", "    # return df['cum_turnover'].iloc[-1] / df['cum_volume'].iloc[-1]\n", "\n", "\n", "def new_analysis(po):\n", "    # 缓存数据加载\n", "    tks = load_tks(po['date'], po['symbol'])\n", "    \n", "    # 确保时间列是Timestamp类型\n", "    tks['time'] = pd.to_datetime(tks['time'])\n", "    \n", "    # 获取时间边界\n", "    starttime, endtime = po['st'], po['et']\n", "    total_duration = (endtime - starttime).total_seconds()\n", "    \n", "    # 计算关键时间点\n", "    t25 = starttime + pd.<PERSON><PERSON><PERSON>(seconds=total_duration * 0.25)\n", "    t50 = starttime + pd.<PERSON><PERSON><PERSON>(seconds=total_duration * 0.5)\n", "    t75 = starttime + pd.<PERSON><PERSON><PERSON>(seconds=total_duration * 0.75)\n", "    \n", "    # 时间过滤 - 获取全时段数据\n", "    interval_tks = tks[(tks['time']>=starttime)&(tks['time']<=endtime)].copy()\n", "    \n", "    # 防御性编程\n", "    if len(interval_tks) < 2:\n", "        return {**po, \n", "                'vwap_0_100': 0, \n", "                'vwap_0_25': 0, \n", "                'vwap_0_50': 0, \n", "                'vwap_25_75': 0,  # 修正变量名\n", "                'vwap_50_100': 0, \n", "                'vwap_75_100': 0, \n", "                'side': -1 if po['operation']==1 else 1}\n", "    \n", "    \n", "    # 计算各时间段VWAP\n", "    # 1. 0-25%时间段 (前25%)\n", "    vwap_0_25 = calculate_vwap(interval_tks[interval_tks['time']<=t25]) if len(interval_tks[interval_tks['time']<=t25]) >= 2 else 0\n", "    \n", "    # 2. 0-50%时间段 (前50%，包含前25%)\n", "    vwap_0_50 = calculate_vwap(interval_tks[interval_tks['time']<=t50]) if len(interval_tks[interval_tks['time']<=t50]) >= 2 else 0\n", "    \n", "    # 3. 25%-75%时间段 (中间50%)\n", "    vwap_25_75 = calculate_vwap(interval_tks[(interval_tks['time']>=t25)&(interval_tks['time']<=t75)]) if len(interval_tks[(interval_tks['time']>=t25)&(interval_tks['time']<=t75)]) >= 2 else 0\n", "    \n", "    # 4. 50%-100%时间段 (后50%，包含后25%)\n", "    vwap_50_100 = calculate_vwap(interval_tks[interval_tks['time']>=t50]) if len(interval_tks[interval_tks['time']>=t50]) >= 2 else 0\n", "    \n", "    # 5. 75%-100%时间段 (后25%)\n", "    vwap_75_100 = calculate_vwap(interval_tks[interval_tks['time']>=t75]) if len(interval_tks[interval_tks['time']>=t75]) >= 2 else 0\n", "    \n", "    # 6. 0-100%全时段\n", "    vwap_0_100 = calculate_vwap(interval_tks)\n", "    \n", "    # 交易方向\n", "    side = -1 if po['operation']==1 else 1\n", "    \n", "    # 返回结果\n", "    return {\n", "        **po,\n", "        'vwap_0_100': vwap_0_100,  # 全时段(0-100%)\n", "        'vwap_0_25': vwap_0_25,    # 前25%\n", "        'vwap_0_50': vwap_0_50,    # 前50%(包含前25%)\n", "        'vwap_25_75': vwap_25_75,  # 中间50%(25%-75%)\n", "        'vwap_50_100': vwap_50_100,# 后50%(包含后25%)\n", "        'vwap_75_100': vwap_75_100,# 后25%\n", "        'side': side\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sd=\"20250601\"\n", "ed=\"20250630\"\n", "sql=\"select * from algo_parentorder where date>='{}' and date<='{}' and algo_provider='zhishu'\".format(sd,ed)\n", "pos=mysql.query(mysql.get_zs_trading_data_db_connection(),sql)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(pos))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(pos[['start_time','end_time']])\n", "print(pos['start_time'].min())\n", "print(pos['end_time'].max())\n", "print(pos['end_time'].unique())\n", "key = pos.to_dict('records')[0]\n", "print(key)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pos['st']=pd.to_datetime(pos['date'].astype(str)+pos['start_time'].apply(lambda x:str(x).zfill(6)),format=\"%Y%m%d%H%M%S\")\n", "# pos['et']=pd.to_datetime(pos['date'].astype(str)+pos['end_time'].apply(lambda x:str(x).zfill(6)),format=\"%Y%m%d%H%M%S\")\n", "# pos['mt']=pos['st']+(pos['et']-pos['st'])/2\n", "# result=Parallel(n_jobs=40,max_nbytes=None)(delayed(analysis)(po) for po in pos.to_dict('records'))  \n", "# tca_df=pd.DataFrame([_ for _ in result if _ is not None]) \n", "# tca_df=tca_df.fillna(0)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# tca_df['vwap_cost']=calc_cost(tca_df['filled_price'],tca_df['vwap_full'],tca_df['side'])\n", "# tca_df['vwap_cost_f50']=calc_cost(tca_df['first50_vwap'],tca_df['vwap_full'],tca_df['side'])\n", "# tca_df['vwap_cost_l50']=calc_cost(tca_df['last50_vwap'],tca_df['vwap_full'],tca_df['side'])\n", "# tca_df['executed_notional']=tca_df['filled_price']*tca_df['filled_quantity']\n", "# tca_df=tca_df.fillna(0)\n", "# show_by_name(tca_df,['account_id'],stats)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pos['st']=pd.to_datetime(pos['date'].astype(str)+pos['start_time'].apply(lambda x:str(x).zfill(6)),format=\"%Y%m%d%H%M%S\")\n", "pos['et']=pd.to_datetime(pos['date'].astype(str)+pos['end_time'].apply(lambda x:str(x).zfill(6)),format=\"%Y%m%d%H%M%S\")\n", "# pos['mt']=pos['st']+(pos['et']-pos['st'])/2\n", "result_2=Parallel(n_jobs=40,max_nbytes=None)(delayed(new_analysis)(po) for po in pos.to_dict('records'))  \n", "tca_df_2=pd.DataFrame([_ for _ in result_2 if _ is not None]) \n", "tca_df_2=tca_df_2.fillna(0)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tca_df_2.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_period_performance(df):\n", "    \"\"\"\n", "    计算6个时段的绩效指标\n", "    \"\"\"\n", "    # 确保所有VWAP列都存在\n", "    required_columns = ['vwap_0_100', 'vwap_0_25', 'vwap_0_50', 'vwap_25_75', \n", "                       'vwap_50_100', 'vwap_75_100', 'filled_price', 'side', 'filled_quantity']\n", "    \n", "    missing_cols = [col for col in required_columns if col not in df.columns]\n", "    if missing_cols:\n", "        raise ValueError(f\"缺少必要的列: {missing_cols}\")\n", "    \n", "    # 计算各时段执行成本\n", "    df['vwap_cost_0_100'] = calc_cost(df['filled_price'], df['vwap_0_100'], df['side'])\n", "    df['vwap_cost_0_25'] = calc_cost(df['vwap_0_25'], df['vwap_0_100'], df['side'])\n", "    df['vwap_cost_0_50'] = calc_cost(df['vwap_0_50'], df['vwap_0_100'], df['side'])\n", "    df['vwap_cost_25_75'] = calc_cost(df['vwap_25_75'], df['vwap_0_100'], df['side'])\n", "    df['vwap_cost_50_100'] = calc_cost(df['vwap_50_100'], df['vwap_0_100'], df['side'])\n", "    df['vwap_cost_75_100'] = calc_cost(df['vwap_75_100'], df['vwap_0_100'], df['side'])\n", "    \n", "    # 计算执行名义金额\n", "    df['executed_notional'] = df['filled_price'] * df['filled_quantity']\n", "    \n", "    # 填充NA值\n", "    df = df.fillna(0)\n", "    \n", "    return df\n", "\n", "# 汇总统计\n", "def new_stats(datas):\n", "    d = {}\n", "    d['executed_value'] = datas['executed_notional'].sum()\n", "    d['no_of_porders'] = len(datas)\n", "    d['po_amt_mean'] = (datas['vwap_0_100'] * datas['quantity']).mean()\n", "    d['po_amt_med'] = (datas['vwap_0_100'] * datas['quantity']).median()\n", "    d['vwap'] = datas['vwap_0_100'].mean()\n", "    d['vwap_cost'] = -np.average(\n", "        datas['vwap_cost_0_100'], \n", "        weights=datas['executed_notional']/d['executed_value']\n", "    )\n", "    d['vwap_cost_0_50'] = -np.average(\n", "        datas['vwap_cost_0_50'],\n", "        weights=datas['executed_notional']/d['executed_value']\n", "    )\n", "    d['vwap_cost_50_100'] = -np.average(\n", "        datas['vwap_cost_50_100'], \n", "        weights=datas['executed_notional']/d['executed_value']\n", "    )\n", "    # 新增6个时段的统计\n", "    d['vwap_cost_0_25'] = -np.average(\n", "        datas['vwap_cost_0_25'], \n", "        weights=datas['executed_notional']/d['executed_value']\n", "    )\n", "    d['vwap_cost_25_75'] = -np.average(\n", "        datas['vwap_cost_25_75'], \n", "        weights=datas['executed_notional']/d['executed_value']\n", "    )\n", "    d['vwap_cost_75_100'] = -np.average(\n", "        datas['vwap_cost_75_100'], \n", "        weights=datas['executed_notional']/d['executed_value']\n", "    )\n", "    return d\n", "\n", "\n", "# 计算各时段绩效\n", "tca_df_2 = calculate_period_performance(tca_df_2)\n", "\n", "\n", "\n", "# 输出结果\n", "performance_result = new_stats(tca_df_2)\n", "# print(performance_result)\n", "\n", "# 按账户ID分组展示绩效统计\n", "show_by_name(tca_df_2, ['account_id', 'operation'], new_stats)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_by_multi_name(tca_df_2, ['account_id', 'operation'], new_stats)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}