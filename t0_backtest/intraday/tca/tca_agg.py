import pandas as pd
import tca
from utils import mysql
import pandas as pd

def amt2w(x):
    return round(x/10000,2)

def val2pct(x):
    return round(x*100,1)

def val2bps(x):
    return round(x*10000,1)

def fmt2display(df):
    df['hold_amt']=df['hold_amt'].apply(amt2w)
    df['trd_amt']=df['trd_amt'].apply(amt2w)
    df['fee']=df['fee'].round(2)
    df['profit']=df['profit'].round(2)
    df['profit_ac']=df['profit_ac'].round(2)
    df['turnover']=df['turnover'].apply(val2pct) 
    df['ret2trade_ac']=df['ret2trade_ac'].apply(val2bps)
    df['ret2hold_ac']=df['ret2hold_ac'].apply(val2bps)
    df['win_rate_ac']=df['win_rate_ac'].apply(val2pct)
    df['win_rate_sym_ac']=df['win_rate_sym_ac'].apply(val2pct)
    df['max_exposure_rate']=df['max_exposure_rate'].apply(val2pct)
    df['max_long_exposure_rate']=df['max_long_exposure_rate'].apply(val2pct)
    df['win_loss_ratio_ac']=df['win_loss_ratio_ac'].round(2)
    df=df[['hold_amt','trd_amt','turnover','ret2trade_ac','ret2hold_ac','win_rate_ac','win_rate_sym_ac','win_loss_ratio_ac','max_exposure_rate','max_long_exposure_rate','date']]
    rename_dict = {key: tca.datedf_name_dict[key] for key in df.columns if key in tca.datedf_name_dict}
    df=df.rename(columns=rename_dict)
    return df

def stats_agg_by_date(df):
    d={}
    d['day_num']=len(df)
    d['avg_hold_amt']=df['hold_amt'].mean()
    d['avg_trd_amt']=df['trd_amt'].mean()
    d['fee']=df['fee'].sum()
    d['profit']=df['profit'].sum()
    d['profit_ac']=df['profit_ac'].sum()
    d['avg_turnover']=df['turnover'].mean()
    d['avg_trd_rate']=df['trd_rate'].mean()
    d['annual_turnover']=df['turnover'].mean() *250
    d['ret2hold']=df['ret2hold_ac'].sum()
    d['annual_ret2hold']=(df['ret2hold_ac'].mean())*250
    # d['sym_win_rate']=df['sym_win_rate_ac'].mean()
    d['day_win_rate']=len(df[df['profit_ac']>0])/d['day_num']
    d['max_exposure_rate']=df['max_exposure_rate'].max()
    d['max_long_exposure_rate']=df['max_long_exposure_rate'].max()
    df2=pd.DataFrame([d])
    df2['avg_hold_amt']=df2['avg_hold_amt'].apply(amt2w)
    df2['avg_trd_amt']=df2['avg_trd_amt'].apply(amt2w)
    df2['fee']=df2['fee'].round(1)
    df2['profit']=df2['profit'].round(1)
    df2['profit_ac']=df2['profit_ac'].round(1)
    df2['avg_turnover']=df2['avg_turnover'].apply(val2pct)
    df2['avg_trd_rate']=df2['avg_trd_rate'].apply(val2pct)
    df2['annual_turnover']=df2['annual_turnover'].round(0)
    df2['ret2hold']=df2['ret2hold'].apply(val2pct)
    df2['annual_ret2hold']=df2['annual_ret2hold'].apply(val2pct)
    df2['day_win_rate']=df2['day_win_rate'].apply(val2pct)
    df2['max_exposure_rate']=df2['max_exposure_rate'].apply(val2pct)
    df2['max_long_exposure_rate']=df2['max_long_exposure_rate'].apply(val2pct)
    df2=df2.rename(columns={'day_num':'交易天数', 'avg_hold_amt':'平均底仓金额（万元）', 'avg_trd_amt':'平均交易金额（万元）', 'fee':'费用', 'profit':'盈利', 'profit_ac':'费后盈利',
       'avg_turnover':'平均换手%','avg_trd_rate':'平均开仓率%', 'annual_turnover':'年化换手（倍）', 'ret2hold':'底仓收益%', 'annual_ret2hold':'年化底仓收益%',
       'day_win_rate':'日胜率%', 'max_exposure_rate':'最大敞口%', 'max_long_exposure_rate':'最大资金占用%'})
    return df2

def stats_agg_by_date2(df):
    d={}
    d['day_num']=len(df)
    d['avg_hold_amt']=df['hold_amt'].mean()
    d['avg_trd_amt']=df['trd_amt'].mean()
    d['fee']=df['fee'].sum()
    d['profit']=df['profit'].sum()
    d['profit_ac']=df['profit_ac'].sum()
    d['avg_turnover']=df['turnover'].mean()
    d['annual_turnover']=df['turnover'].mean() *250
    d['ret2hold']=df['ret2hold_ac'].sum()
    d['cancel_rate']=val2pct(df['cancel_rate'].mean())
    d['annual_ret2hold']=(df['ret2hold_ac'].mean())*250
    # d['sym_win_rate']=df['sym_win_rate_ac'].mean()
    d['day_win_rate']=len(df[df['profit_ac']>0])/d['day_num']
    d['max_exposure_rate']=df['max_exposure_rate'].max()
    d['max_long_exposure_rate']=df['max_long_exposure_rate'].max()
    df2=pd.DataFrame([d])
    df2['avg_hold_amt']=df2['avg_hold_amt'].apply(amt2w)
    df2['avg_trd_amt']=df2['avg_trd_amt'].apply(amt2w)
    df2['fee']=df2['fee'].round(1)
    df2['profit']=df2['profit'].round(1)
    df2['profit_ac']=df2['profit_ac'].round(1)
    df2['avg_turnover']=df2['avg_turnover'].apply(val2pct)
    df2['annual_turnover']=df2['annual_turnover'].round(0)
    df2['ret2hold']=df2['ret2hold'].apply(val2bps)
    df2['annual_ret2hold']=df2['annual_ret2hold'].apply(val2pct)
    df2['day_win_rate']=df2['day_win_rate'].apply(val2pct)
    df2['max_exposure_rate']=df2['max_exposure_rate'].apply(val2pct)
    df2['max_long_exposure_rate']=df2['max_long_exposure_rate'].apply(val2pct)
    df2=df2.rename(columns={'day_num':'交易天数', 'avg_hold_amt':'平均底仓金额（万元）', 'avg_trd_amt':'平均交易金额（万元）', 'fee':'费用', 'profit':'盈利', 'profit_ac':'费后盈利',
       'avg_turnover':'平均换手%', 'annual_turnover':'年化换手（倍）', 'ret2hold':'底仓收益(bps)', 'annual_ret2hold':'年化底仓收益%','cancel_rate':'撤单率%',
       'day_win_rate':'日胜率%', 'max_exposure_rate':'最大敞口%', 'max_long_exposure_rate':'最大资金占用%'})
    return df2