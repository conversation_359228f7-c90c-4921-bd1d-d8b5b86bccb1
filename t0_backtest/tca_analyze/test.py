import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# 添加项目路径以便导入 mysql 工具
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'intraday'))
from utils import mysql


def create_vwap_cache_table():
    """创建 VWAP 缓存表"""
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS vwap_price (
        id INT AUTO_INCREMENT PRIMARY KEY,
        date VARCHAR(8) NOT NULL,
        symbol VARCHAR(10) NOT NULL,
        start_time TIME NOT NULL,
        end_time TIME NOT NULL,
        vwap_price DECIMAL(10, 4) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_vwap (date, symbol, start_time, end_time),
        INDEX idx_date_symbol (date, symbol),
        INDEX idx_time_range (start_time, end_time)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VWAP价格缓存表';
    """

    try:
        conn = mysql.get_zs_trading_data_db_connection(engine=True)
        with conn.connect() as connection:
            connection.execute(mysql.text(create_table_sql))
            connection.commit()
        print("VWAP 缓存表创建成功")
    except Exception as e:
        print(f"创建 VWAP 缓存表失败: {e}")


def get_vwap_from_cache(date, symbol, start_time, end_time):
    """从缓存中获取 VWAP 价格"""
    query_sql = """
    SELECT vwap_price FROM vwap_price
    WHERE date = %s AND symbol = %s AND start_time = %s AND end_time = %s
    """

    try:
        conn = mysql.get_zs_trading_data_db_connection(engine=False)
        cursor = conn.cursor()
        cursor.execute(query_sql, (date, symbol, start_time, end_time))
        result = cursor.fetchone()
        cursor.close()
        conn.close()

        if result:
            return result[0]  # 返回 vwap_price
        return None
    except Exception as e:
        print(f"查询 VWAP 缓存失败: {e}")
        return None


def save_vwap_to_cache(date, symbol, start_time, end_time, vwap_price):
    """将 VWAP 价格保存到缓存"""
    insert_sql = """
    INSERT INTO vwap_price (date, symbol, start_time, end_time, vwap_price)
    VALUES (%s, %s, %s, %s, %s)
    ON DUPLICATE KEY UPDATE
        vwap_price = VALUES(vwap_price),
        updated_at = CURRENT_TIMESTAMP
    """

    try:
        conn = mysql.get_zs_trading_data_db_connection(engine=False)
        cursor = conn.cursor()
        cursor.execute(insert_sql, (date, symbol, start_time, end_time, vwap_price))
        conn.commit()
        cursor.close()
        conn.close()
        print(f"VWAP 缓存保存成功: {date} {symbol} {start_time}-{end_time} = {vwap_price}")
    except Exception as e:
        print(f"保存 VWAP 缓存失败: {e}")



def analysis(po):
    """
    分析函数，带 VWAP 缓存功能

    参数:
        po: 包含以下字段的字典
            - date: 日期 (格式: '20250625')
            - symbol: 股票代码 (格式: '600000')
            - st: 开始时间
            - et: 结束时间
            - mt: 中间时间
            - operation: 操作类型
            - id: 订单ID
    """
    date = po['date']
    symbol = po['symbol']
    starttime = po['st']
    endtime = po['et']
    middletime = po['mt']

    # 首先尝试从缓存中获取 VWAP
    cached_vwap = get_vwap_from_cache(date, symbol, starttime, endtime)

    if cached_vwap is not None:
        print(f"从缓存获取 VWAP: {date} {symbol} {starttime}-{endtime} = {cached_vwap}")
        vwap_full = float(cached_vwap)
    else:
        print(f"缓存未命中，重新计算 VWAP: {date} {symbol} {starttime}-{endtime}")
        # 读取数据并计算 VWAP
        tks = pd.read_feather(r"/data/shared-data/public/snapshot/syms/{}/{}".format(date, symbol))
        interval_tks = tks[(tks['time'] >= starttime) & (tks['time'] <= endtime)]

        if len(interval_tks) < 2:
            print(f"警告: 时间区间内数据不足，无法计算 VWAP")
            vwap_full = 0.0
        else:
            start_turnover = interval_tks.iloc[0]['Turnover']
            end_turnover = interval_tks.iloc[-1]['Turnover']
            start_volume = interval_tks.iloc[0]['TradVolume']
            end_volume = interval_tks.iloc[-1]['TradVolume']

            volume_diff = end_volume - start_volume
            if volume_diff == 0:
                print(f"警告: 时间区间内成交量为0，无法计算 VWAP")
                vwap_full = 0.0
            else:
                vwap_full = (end_turnover - start_turnover) / volume_diff

                # 将计算结果保存到缓存
                save_vwap_to_cache(date, symbol, starttime, endtime, vwap_full)

    # 构建返回结果
    side = -1 if po['operation'] == 1 else 1
    d = {
        'id': po['id'],
        'vwap_full': vwap_full,
        'side': side
    }
    d.update(po)
    return d


date = '20250625'
symbol = '600000'
df_4 = pd.read_feather(f"/data/shared-data/public/snapshot/syms/{date}/{symbol}")

print(df_4.head())
