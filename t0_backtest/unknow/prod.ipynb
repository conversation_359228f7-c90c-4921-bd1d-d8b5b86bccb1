{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["libPath: /home/<USER>/trade/t0_backtest/intraday\n"]}], "source": ["\n", "# from intraday.tca_new import tca\n", "\n", "import pandas as pd\n", "import os, sys\n", "\n", "from pathlib import Path\n", "\n", "notebook_dir = Path.cwd() \n", "libPath = str(notebook_dir.parent) + '/intraday'\n", "print(f'libPath: {libPath}')\n", "sys.path.insert(0, libPath)\n", "from data import data_reader \n", "from utils import mysql\n", "\n", "from tca import tca_agg\n", "import new_tca as tca\n", "sys.path.remove(libPath)\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["date=\"20250718\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/trade/t0_backtest/intraday/utils/mysql.py:91: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.\n", "  return pd.read_sql(sql,conn)\n", "/home/<USER>/trade/t0_backtest/intraday/utils/mysql.py:91: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.\n", "  return pd.read_sql(sql,conn)\n"]}, {"ename": "Exception", "evalue": "execute sql failed Execution failed on sql 'select * from orders where pid in ()': (1064, \"You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1\")", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mProgrammingError\u001b[39m                          <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pandas/io/sql.py:2674\u001b[39m, in \u001b[36mSQLiteDatabase.execute\u001b[39m\u001b[34m(self, sql, params)\u001b[39m\n\u001b[32m   2673\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m2674\u001b[39m     \u001b[43mcur\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43msql\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   2675\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m cur\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pymysql/cursors.py:153\u001b[39m, in \u001b[36mCursor.execute\u001b[39m\u001b[34m(self, query, args)\u001b[39m\n\u001b[32m    151\u001b[39m query = \u001b[38;5;28mself\u001b[39m.mogrify(query, args)\n\u001b[32m--> \u001b[39m\u001b[32m153\u001b[39m result = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_query\u001b[49m\u001b[43m(\u001b[49m\u001b[43mquery\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    154\u001b[39m \u001b[38;5;28mself\u001b[39m._executed = query\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pymysql/cursors.py:322\u001b[39m, in \u001b[36mCursor._query\u001b[39m\u001b[34m(self, q)\u001b[39m\n\u001b[32m    321\u001b[39m \u001b[38;5;28mself\u001b[39m._clear_result()\n\u001b[32m--> \u001b[39m\u001b[32m322\u001b[39m \u001b[43mconn\u001b[49m\u001b[43m.\u001b[49m\u001b[43mquery\u001b[49m\u001b[43m(\u001b[49m\u001b[43mq\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    323\u001b[39m \u001b[38;5;28mself\u001b[39m._do_get_result()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pymysql/connections.py:563\u001b[39m, in \u001b[36mConnection.query\u001b[39m\u001b[34m(self, sql, unbuffered)\u001b[39m\n\u001b[32m    562\u001b[39m \u001b[38;5;28mself\u001b[39m._execute_command(COMMAND.COM_QUERY, sql)\n\u001b[32m--> \u001b[39m\u001b[32m563\u001b[39m \u001b[38;5;28mself\u001b[39m._affected_rows = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_read_query_result\u001b[49m\u001b[43m(\u001b[49m\u001b[43munbuffered\u001b[49m\u001b[43m=\u001b[49m\u001b[43munbuffered\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    564\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._affected_rows\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pymysql/connections.py:825\u001b[39m, in \u001b[36mConnection._read_query_result\u001b[39m\u001b[34m(self, unbuffered)\u001b[39m\n\u001b[32m    824\u001b[39m     result = MySQLResult(\u001b[38;5;28mself\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m825\u001b[39m     \u001b[43mresult\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    826\u001b[39m \u001b[38;5;28mself\u001b[39m._result = result\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pymysql/connections.py:1199\u001b[39m, in \u001b[36mMySQLResult.read\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1198\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1199\u001b[39m     first_packet = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mconnection\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_read_packet\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1201\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m first_packet.is_ok_packet():\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pymysql/connections.py:775\u001b[39m, in \u001b[36mConnection._read_packet\u001b[39m\u001b[34m(self, packet_type)\u001b[39m\n\u001b[32m    774\u001b[39m         \u001b[38;5;28mself\u001b[39m._result.unbuffered_active = \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m775\u001b[39m     \u001b[43mpacket\u001b[49m\u001b[43m.\u001b[49m\u001b[43mraise_for_error\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    776\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m packet\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pymysql/protocol.py:219\u001b[39m, in \u001b[36mMysqlPacket.raise_for_error\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    218\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33merrno =\u001b[39m\u001b[33m\"\u001b[39m, errno)\n\u001b[32m--> \u001b[39m\u001b[32m219\u001b[39m \u001b[43merr\u001b[49m\u001b[43m.\u001b[49m\u001b[43mraise_mysql_exception\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_data\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pymysql/err.py:150\u001b[39m, in \u001b[36mraise_mysql_exception\u001b[39m\u001b[34m(data)\u001b[39m\n\u001b[32m    149\u001b[39m     errorclass = InternalError \u001b[38;5;28;01mif\u001b[39;00m errno < \u001b[32m1000\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m OperationalError\n\u001b[32m--> \u001b[39m\u001b[32m150\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m errorclass(errno, errval)\n", "\u001b[31mProgrammingError\u001b[39m: (1064, \"You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1\")", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mDatabaseError\u001b[39m                             Traceback (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/trade/t0_backtest/intraday/utils/mysql.py:91\u001b[39m, in \u001b[36mquery\u001b[39m\u001b[34m(conn, sql, DataFrame)\u001b[39m\n\u001b[32m     90\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m DataFrame:\n\u001b[32m---> \u001b[39m\u001b[32m91\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mpd\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread_sql\u001b[49m\u001b[43m(\u001b[49m\u001b[43msql\u001b[49m\u001b[43m,\u001b[49m\u001b[43mconn\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     92\u001b[39m cur.execute(sql)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pandas/io/sql.py:706\u001b[39m, in \u001b[36mread_sql\u001b[39m\u001b[34m(sql, con, index_col, coerce_float, params, parse_dates, columns, chunksize, dtype_backend, dtype)\u001b[39m\n\u001b[32m    705\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(pandas_sql, SQLiteDatabase):\n\u001b[32m--> \u001b[39m\u001b[32m706\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mpandas_sql\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread_query\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    707\u001b[39m \u001b[43m        \u001b[49m\u001b[43msql\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    708\u001b[39m \u001b[43m        \u001b[49m\u001b[43mindex_col\u001b[49m\u001b[43m=\u001b[49m\u001b[43mindex_col\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    709\u001b[39m \u001b[43m        \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m=\u001b[49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    710\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcoerce_float\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcoerce_float\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    711\u001b[39m \u001b[43m        \u001b[49m\u001b[43mparse_dates\u001b[49m\u001b[43m=\u001b[49m\u001b[43mparse_dates\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    712\u001b[39m \u001b[43m        \u001b[49m\u001b[43mchunksize\u001b[49m\u001b[43m=\u001b[49m\u001b[43mchunksize\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    713\u001b[39m \u001b[43m        \u001b[49m\u001b[43mdtype_backend\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdtype_backend\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    714\u001b[39m \u001b[43m        \u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    715\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    717\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pandas/io/sql.py:2738\u001b[39m, in \u001b[36mSQLiteDatabase.read_query\u001b[39m\u001b[34m(self, sql, index_col, coerce_float, parse_dates, params, chunksize, dtype, dtype_backend)\u001b[39m\n\u001b[32m   2727\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[34mread_query\u001b[39m(\n\u001b[32m   2728\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   2729\u001b[39m     sql,\n\u001b[32m   (...)\u001b[39m\u001b[32m   2736\u001b[39m     dtype_backend: DtypeBackend | Literal[\u001b[33m\"\u001b[39m\u001b[33mnumpy\u001b[39m\u001b[33m\"\u001b[39m] = \u001b[33m\"\u001b[39m\u001b[33mnumpy\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m   2737\u001b[39m ) -> DataFrame | Iterator[DataFrame]:\n\u001b[32m-> \u001b[39m\u001b[32m2738\u001b[39m     cursor = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43msql\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   2739\u001b[39m     columns = [col_desc[\u001b[32m0\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m col_desc \u001b[38;5;129;01min\u001b[39;00m cursor.description]\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.12/site-packages/pandas/io/sql.py:2686\u001b[39m, in \u001b[36mSQLiteDatabase.execute\u001b[39m\u001b[34m(self, sql, params)\u001b[39m\n\u001b[32m   2685\u001b[39m ex = DatabaseError(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mExecution failed on sql \u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00msql\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mexc\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m-> \u001b[39m\u001b[32m2686\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m ex \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[34;01mexc\u001b[39;00m\n", "\u001b[31mDatabaseError\u001b[39m: Execution failed on sql 'select * from orders where pid in ()': (1064, \"You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1\")", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31mException\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m pords=mysql.query(mysql.get_zs_trading_data_db_connection(),\u001b[33m\"\u001b[39m\u001b[33mselect * from intraday_parentorder where date=\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[33m and algo_provider=\u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m\"\u001b[39m.format(date,\u001b[33m'\u001b[39m\u001b[33mzhishu\u001b[39m\u001b[33m'\u001b[39m))\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m ords=\u001b[43mmysql\u001b[49m\u001b[43m.\u001b[49m\u001b[43mquery\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmysql\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget_zs_trading_data_db_connection\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mselect * from orders where pid in \u001b[39;49m\u001b[38;5;132;43;01m{}\u001b[39;49;00m\u001b[33;43m\"\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mformat\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mtuple\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mpords\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mid\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m.\u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/trade/t0_backtest/intraday/utils/mysql.py:95\u001b[39m, in \u001b[36mquery\u001b[39m\u001b[34m(conn, sql, DataFrame)\u001b[39m\n\u001b[32m     93\u001b[39m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m cur.fetchall()\n\u001b[32m     94\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m---> \u001b[39m\u001b[32m95\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mexecute sql failed \u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[33m\"\u001b[39m.format(e))       \n\u001b[32m     96\u001b[39m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[32m     97\u001b[39m     cur.close()\n", "\u001b[31mException\u001b[39m: execute sql failed Execution failed on sql 'select * from orders where pid in ()': (1064, \"You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1\")"]}], "source": ["pords=mysql.query(mysql.get_zs_trading_data_db_connection(),\"select * from intraday_parentorder where date={} and algo_provider='{}'\".format(date,'zhishu'))\n", "ords=mysql.query(mysql.get_zs_trading_data_db_connection(),\"select * from orders where pid in {}\".format(tuple(pords['id'].values)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ords=ords.fillna(0)\n", "pxs=data_reader.get_close_pxs_by_date_from_file(date)\n", "pos,trds=tca.get_transaction_and_position(pords,ords,pxs)\n", "max_exposure,max_long_exposure=tca.get_exposure(ords)\n", "rets=tca.stats_by_pords(pords,ords,pos,trds)\n", "tca_df=pd.DataFrame(rets).fillna(0)\n", "tca_df['date']=date\n", "tca_df['max_exposure']=max_exposure\n", "tca_df['max_long_exposure']=max_long_exposure"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tca_df['sys_type'].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tca_agg.stats_agg_by_date2(pd.DataFrame([tca.stats_agg_by_date(tca_df[tca_df['sys_type']=='kafang_zongxian'])]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pords"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tca_agg.stats_agg_by_date2(pd.DataFrame([tca.stats_agg_by_date(tca_df[tca_df['sys_type']=='zhongtai_tuoguan'])]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ords"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pos"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ords"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2=tca_df[tca_df['sys_type']=='zhongtai_tuoguan']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pos"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2.sort_values('profit')[['symbol','profit']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ords[ords['symbol']=='300047']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2.sort_values('profit_ac')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pords['sys_type'].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pos"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pords.to_csv('pords.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from utils import trading_calendar"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dates=trading_calendar.get_trading_dates(\"20250323\",\"20250328\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l=[]\n", "l2=[]\n", "for date in dates:\n", "    print(date)\n", "    pords=mysql.query(mysql.get_zs_trading_data_db_connection(),\"select * from intraday_parentorder where date={}\".format(date))\n", "    if len(pords)==0:\n", "        continue\n", "    ords=mysql.query(mysql.get_zs_trading_data_db_connection(),\"select * from orders where pid in {}\".format(tuple(pords['id'].values)))\n", "    ords=ords.fillna(0)\n", "    pxs=data_reader.get_close_pxs_by_date_from_file(date)\n", "    pos,trds=tca.get_transaction_and_position(pords,ords,pxs)\n", "    max_exposure,max_long_exposure=tca.get_exposure(ords)\n", "    rets=tca.stats_by_pords(pords,ords,pos,trds)\n", "    tca_df=pd.DataFrame(rets).fillna(0)\n", "    tca_df['date']=date\n", "    tca_df['max_exposure']=max_exposure\n", "    tca_df['max_long_exposure']=max_long_exposure\n", "    l2.append(tca_df)\n", "    l.append(tca.stats_agg_by_date(tca_df))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tca_agg.stats_agg_by_date2(pd.DataFrame(l))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df=pd.concat(l2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2=df[df['date']>'20250323']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2['exchange']=df2['symbol'].apply(lambda x:'sh' if x[0]=='6' else 'sz')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sh=df2[df2['exchange']=='sh']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sz=df2[df2['exchange']=='sz']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sh['cancel_ord_num'].sum()/(sh['ord_num']-sh['reject_ord_num']).sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sz['cancel_ord_num'].sum()/(sz['ord_num']-sz['reject_ord_num']).sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df3=df[(df['date']>'20250315')&(df['date']<'20250323')]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df3['exchange']=df3['symbol'].apply(lambda x:'sh' if x[0]=='6' else 'sz')\n", "sh=df3[df3['exchange']=='sh']\n", "sz=df3[df3['exchange']=='sz']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sh['cancel_ord_num'].sum()/(sh['ord_num']-sh['reject_ord_num']).sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sz['cancel_ord_num'].sum()/(sz['ord_num']-sz['reject_ord_num']).sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}