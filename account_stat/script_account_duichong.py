import os, sys
from idna import encode
import pandas as pd
import zipfile

from sqlalchemy import all_
current_dir = os.path.dirname(os.path.abspath(__file__))

# 将项目路径添加到模块搜索路径
project_dir = os.path.abspath(os.path.join(current_dir, ".."))  # 通过..返回上一级目录
sys.path.append(project_dir)


from loguru import logger

import datetime
from misc.Readstockfile import read_remote_file, write_file
from misc.ssh_conn import sftp_clent_wintrader, ftp_clent_zx_zhongtai
import warnings
warnings.filterwarnings("ignore")

# def copy_files(date):
#     pass
    # src_dir = f'数据导出/中泰SmartX/{date}/'
    # target_dir = f'/home/<USER>/dav/accounts/超量子中泰/客户端导出/{date}/'
    # if not os.path.exists(target_dir):
    #     os.makedirs(target_dir)
    
    # files = [
    #     f'basket_{date}.csv',
    #     f'parentorder_{date}.csv',
    #     f'order_{date}.csv',
    #     f'transaction_{date}.csv',
    #     f't0_basket_{date}.csv',
    #     f't0_parentorder_{date}.csv',
    #     f't0_order_{date}.csv',
    #     f't0_transaction_{date}.csv',
    # ]
    # for file in files:
    #     sftp_clent_wintrader.get(os.path.join(src_dir, file), os.path.join(target_dir, file))
        
        
    # src_dir = f'数据导出/中泰SmartX/自动导出/{date}/'
    # target_dir = f'/home/<USER>/dav/accounts/超量子中泰/客户端导出/{date}/'
    # if not os.path.exists(target_dir):
    #     os.makedirs(target_dir)
    
    # files = [
    #     'xtp_109156033251_algoList.csv',
    #     'xtp_109156033251_algoTickerList.csv',
    #     'xtp_109156033251_Asset.csv',
    #     'xtp_109156033251_Order.csv',
    #     'xtp_109156033251_Position.csv',
    #     'xtp_109156033251_Trade.csv',
    # ]
    # for file in files:
    #     sftp_clent_wintrader.get(os.path.join(src_dir, file), os.path.join(target_dir, file))


# def filter_tuoguan_t0(date):
#     file = 'xtp_109156033251_Trade.csv'
#     target_file = f't0_transaction_2_{date}.csv'
#     target_dir = f'/home/<USER>/dav/accounts/超量子中泰/客户端导出/{date}/'
    
#     df = pd.read_csv(os.path.join(target_dir, file))
#     df = df[df['渠道'].isna()]
#     print(df.head())
#     print(df.shape)
    
#     df.to_csv(os.path.join(target_dir, target_file), index=False, encoding='gbk')

def upzip_file(date):
    src_dir = f'/home/<USER>/dav/accounts/中性对冲_国君股票/客户端导出/{date}/'
    zip_file = f'data({date}).zip'
    #upzip
    with zipfile.ZipFile(os.path.join(src_dir, zip_file), 'r') as zip_ref:
        zip_ref.extractall(src_dir)

    
def upload_files(date):
    src_dir = f'/home/<USER>/dav/accounts/中性对冲_国君股票/客户端导出/{date}/'
    target_dir = f'duichong/daily_after/'
    if date not in ftp_clent_zx_zhongtai.listdir(target_dir):
        ftp_clent_zx_zhongtai.mkdir(os.path.join(target_dir, date))
    
    
    to_upload_files = [
        'algoActual.csv',
        'algoNominal.csv',
        'algoSummaryBasketId.csv',
        'algoSummaryClient.csv',
        'T0Actual.csv',
        'T0Nominal.csv',
        'T0Overnight.csv',
        'T0SummaryClient.csv',
    ]
    all_dir_files = os.listdir(src_dir)
    for file in all_dir_files:
        if file.startswith(f'account_{date}') or file.startswith(f'资金信息_{date}'):
            os.rename(os.path.join(src_dir, file), os.path.join(src_dir, f'account_{date}.xlsx'))
            
            to_upload_files.append(f'account_{date}.xlsx')
        if file.startswith(f'position_{date}') or file.startswith(f'持仓查询_{date}'):
            os.rename(os.path.join(src_dir, file), os.path.join(src_dir, f'position_{date}.xlsx'))
            to_upload_files.append(f'position_{date}.xlsx')
        if file.startswith(f'trade_{date}') or file.startswith(f'委托查询_{date}'):
            os.rename(os.path.join(src_dir, file), os.path.join(src_dir, f'trade_{date}.xlsx'))
            to_upload_files.append(f'trade_{date}.xlsx')
        if file.startswith(f'委托详情032105'):
            os.rename(os.path.join(src_dir, file), os.path.join(src_dir, f't0_transaction_{date}.csv'))
            to_upload_files.append(f't0_transaction_{date}.csv')
    
    for file in to_upload_files:
        ftp_clent_zx_zhongtai.put(os.path.join(src_dir, file), os.path.join(target_dir, date, file))
    

def record_account_info(date):
    src_dir = f'/home/<USER>/dav/accounts/中性对冲_国君股票/客户端导出/{date}/'
    target_dir = f'/home/<USER>/dav/accounts/中性对冲_国君股票/account/'
    
    all_dir_files = os.listdir(src_dir)
    for file in all_dir_files:
        if file.startswith(f'account_{date}'):
            src_file = os.path.join(src_dir, file)
        
    df = pd.read_excel(os.path.join(src_dir, src_file), dtype={'资金账号': str})

    account_id = '10573688_2105_8382'
    # trim the space
    df = df.map(lambda x: x.strip() if isinstance(x, str) else x)

    # for col in df.columns:
    #     if col != '资金账号':
    #         df[col] = df[col].astype(float)
    
    df.set_index('资金账号', inplace=True)

    df2 = pd.DataFrame()
    df2.loc[0, 'date']    = date
    df2.loc[0, 'net_asset']      = df.loc[account_id, '总资产']
    df2.loc[0, 'available_fund'] = df.loc[account_id, '资金余额']
    df2.loc[0, 'stock_value']    = df.loc[account_id, '总市值']
    df2.to_csv(os.path.join(target_dir, f'accountinfo_{date}.csv'), index=False, encoding='gbk')
    
    
def upload_futures_files(date):
    src_dir = f'/home/<USER>/dav/accounts/中性对冲_申万期货'
    target_dir = f'duichong/daily_after/'
    if date not in ftp_clent_zx_zhongtai.listdir(target_dir):
        ftp_clent_zx_zhongtai.mkdir(os.path.join(target_dir, date))
        
    src_hold_file = f'hold_{date}.csv'
    target_hold_file = f'futures_hold_{date}.csv'
    
    src_trade_file = f'futuredeal_{date}.csv'
    target_trade_file = f'futuredeal_{date}.csv'
    
    ftp_clent_zx_zhongtai.put(os.path.join(src_dir, 'hold', src_hold_file), os.path.join(target_dir, date, target_hold_file))
    
    if os.path.exists(os.path.join(src_dir, 'account', src_trade_file)):
        ftp_clent_zx_zhongtai.put(os.path.join(src_dir, 'account', src_trade_file), os.path.join(target_dir, date, target_trade_file))
    
    
if __name__ == '__main__':
    if len(sys.argv) > 1:
        date = str(sys.argv[1])
    else:
        date = datetime.datetime.now().strftime('%Y%m%d')
    
    # copy_files(date)
    
    upzip_file(date)
    upload_files(date)
    record_account_info(date)
    upload_futures_files(date)
    
    
    # for date in [
    #     '********',
    #     '********',
    #     '********',
    #     '********',
    #     '********',
    #     '********',
    #     '********',
    #     '********',
    #     '********',
    #     '********',
    #     '********',
    #     '********',
    #     '********',
    #     '********',
    #     '********',
    #     '********',
    #     '********',
    #     '********',

    # ]: