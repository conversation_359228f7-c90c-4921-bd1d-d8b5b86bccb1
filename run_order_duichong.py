import datetime
from tracemalloc import start
from turtle import st
from duckdb import order
from matplotlib.pyplot import flag
import pandas as pd
import numpy as np
import shutil
import os, sys
import pprint

from misc.ssh_conn import sftp_clent_wintrader, ftp_clent_chaolz
from misc.tools import get_stock_adj_close
from data_utils.trading_calendar import Calendar
from misc.Readstockfile import read_remote_file
# from misc.standardize_dev.zhongtai_smartx import std_hold
from misc.convert_orders import convert_kafang_atx_orders


from misc.trade import cal_changes

def get_target_parentorders(work_date=None, suffix=''):
    if work_date is None:
        work_date = datetime.datetime.now().strftime("%Y%m%d")
        # targetPosition_  109156033251_Position{}_Target
    # targetFile = "/home/<USER>/targetPosition_{}.csv".format(work_date,work_date)  
    # targetFile = "targetPosition_{}.csv".format(work_date,work_date)
    targetFile = "/data/shared-data/public/vsftp_data/zs_zhongtai/data/duichong/daily_before/{}/target_duichong_{}{}.csv".format(work_date,work_date,suffix)
    try:
        df = pd.read_csv(targetFile)
        # print(df.head(3))
        return df
    except FileNotFoundError:
        print(f"文件 {targetFile} 未找到。")
    except Exception as e:
        print(f"读取文件时发生错误: {e}")

def GetExchange(sym):
    if sym[:1] == "6":
        return sym+".SH"
    return sym+".SZ"

def GetOnlyExchangeCn(sym):
    if sym[:1] == "6":
        return "上交所"
    elif sym[:1] == "3" or sym[:1] == "0":
        return "深交所"
    else:
        raise ValueError(f'symbol {sym} not supported')

def stk_lot_size(sym):
    if sym[:3]=="688":
        return 200
    return 100     


def get_target(work_chaonne, suffix=''):
    """
    return symbol, target_shares, direction
    """
    targets = get_target_parentorders(work_date, suffix)
    targets_df = targets.copy()
    targets_df['symbol']=targets_df['symbol'].astype(int)
    targets_df.rename(columns={'target': 'target_shares', }, inplace=True)
    targets_df['direction'] = targets_df['target_shares'].apply(lambda x: 1 if x > 0 else -1)
    targets_df['target_shares'] = targets_df['target_shares'].abs()
    
    return targets_df[['symbol', 'target_shares', 'direction']]


# def get_hold(work_date):
#     """
#     return symbol, hold_shares, available_shares, direction
#     """
#     hold_file = f'数据导出/中泰SmartX/自动导出/{work_date}/xtp_109156033251_Position.csv'
#     df = read_remote_file(hold_file, src_type='wintrader')
#     df = std_hold(df)
#     df = df[['ticker', 'volume', 'available_volume']].rename(columns={'ticker': 'symbol', 'volume': 'hold_shares', 'available_volume': 'available_shares'})
#     df['direction'] = df['hold_shares'].apply(lambda x: 1 if x > 0 else -1)
#     df['hold_shares'] = df['hold_shares'].abs()
#     return df


def get_hold_from_targetorder(work_date, suffix=''):
    """
    return symbol, hold_shares, available_shares, direction
    """
    targets = get_target_parentorders(work_date, suffix)
    hold = targets.copy()
    
    hold['symbol'] = hold['symbol'].astype(int)
    hold = hold[['symbol', 'position']]
    hold.rename(columns={'position': 'hold_shares'}, inplace=True)
    hold['available_shares'] = hold['hold_shares']
    hold['direction'] = hold['hold_shares'].apply(lambda x: 1 if x > 0 else -1)
    hold['hold_shares'] = hold['hold_shares'].abs()
    return hold


def get_target_as_hold(work_date, suffix=''):
    """
    return symbol, hold_shares, available_shares, direction
    """
    targets = get_target_parentorders(work_date, suffix)
    hold = targets.copy()
    
    hold['symbol'] = hold['symbol'].astype(int)
    hold['available_shares'] = hold[['target', 'position']].min(axis=1)
    hold.rename(columns={'target': 'hold_shares'}, inplace=True)
    hold = hold[['symbol', 'hold_shares', 'available_shares']]
    
    hold['direction'] = hold['hold_shares'].apply(lambda x: 1 if x > 0 else -1)
    hold['hold_shares'] = hold['hold_shares'].abs()
    return hold



def vwap_target(work_date, hold_type='from_target', target_suffix='', order_suffix='', **kwargs):
    targets_df = get_target(work_date, target_suffix)
    
    # if hold_type == 'latest':
    #     hold_df = get_hold(work_date)
    if hold_type == 'from_target':
        hold_df = get_hold_from_targetorder(work_date, target_suffix)
    elif hold_type == 'as_target':
        hold_df = get_target_as_hold(work_date, target_suffix)
    else:
        raise ValueError(f'hold_type {hold_type} is not supported.')
    
    
    pre_date = Calendar.last_trading_day(work_date).strftime('%Y%m%d')
    pre_close = get_stock_adj_close(pre_date).rename(columns={'ticker': 'symbol',})
    changes, info = cal_changes(hold_df, targets_df, pre_close)
    
    trade_args = {
        'order_type' : 'kafang_atx',
        'account_id' : '鲲洋远如量化1号证券',
        'strategy_type' : 'VWAP',
        'trading_date' : work_date,
        'strategy_instance' : 'HX_SMART_VWAP',
        
        # 'strategy_instance' : 'ld_vwap',
        'strategy_paras' : {
            # '市场参与率' : 10,    # ld_vwap 参数
            '篮子编号' : 'zxdc_{}'.format(datetime.datetime.now().strftime('%H%M')),
        },
        'start_time' : kwargs.get('start_time', '093000'),
        'end_time' : kwargs.get('end_time', '100000'),
    }
    orders = convert_kafang_atx_orders(changes, **trade_args)
    
    local_account_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'accounts', account_name)
    if not os.path.exists(local_account_dir):
        os.makedirs(local_account_dir)
    csv_file_path = os.path.join(local_account_dir, f'{account_name}_vwap_target_{work_date}{order_suffix}.csv')
    orders.to_csv(csv_file_path, index=False, encoding='gbk')
    


def t0_target(work_date, hold_type='from_target', target_suffix='', order_suffix='', **kwargs):
    targets_df = get_target(work_date, target_suffix)
    
    # if hold_type == 'latest':
    #     hold_df = get_hold(work_date)
    if hold_type == 'from_target':
        hold_df = get_hold_from_targetorder(work_date, target_suffix)
    elif hold_type == 'as_target':
        hold_df = get_target_as_hold(work_date, target_suffix)
    else:
        raise ValueError(f'hold_type {hold_type} is not supported.')
    
    df = pd.concat([targets_df[['symbol', 'target_shares']].set_index('symbol'), hold_df[['symbol', 'available_shares']].set_index('symbol')], axis=1, join='outer')
    df.fillna(0, inplace=True)
    df['t0_shares'] = df[['target_shares', 'available_shares']].min(axis=1)    
    df['t0_shares'] = df['t0_shares'].div(100).astype('int').mul(100)
    df['t0_shares'] = np.where((df.index >= 688000) & (df['t0_shares'] < 200), 0, df['t0_shares'])
    df = df[['t0_shares']].reset_index()
    df = df[df['t0_shares'] != 0]
    
    cols = [
        'code',
        'volume',
        'buyCredit',
        'sellCredit',
    ]
    order = pd.DataFrame(columns=cols)
    order['code'] = df['symbol'].astype(str).str.zfill(6).apply(GetExchange)
    order['volume'] = df['t0_shares'].tolist()
    order['buyCredit'] = ''
    order['sellCredit'] = ''    
    
    local_account_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'accounts', account_name)
    csv_file_path = os.path.join(local_account_dir, f'{account_name}_t0_target_{work_date}{order_suffix}.csv')
    order.to_csv(csv_file_path, index=False, encoding='gbk')
    print(f't0 orders:\n{order.head(5)}')
    print(f't0 总股数: {order["volume"].sum():,.0f}')
    


# def t0_target(work_date, hold_type='from_target', target_suffix='', order_suffix='', **kwargs):
#     targets_df = get_target(work_date, target_suffix)
    
#     # if hold_type == 'latest':
#     #     hold_df = get_hold(work_date)
#     if hold_type == 'from_target':
#         hold_df = get_hold_from_targetorder(work_date, target_suffix)
#     elif hold_type == 'as_target':
#         hold_df = get_target_as_hold(work_date, target_suffix)
#     else:
#         raise ValueError(f'hold_type {hold_type} is not supported.')
    
#     df = pd.concat([targets_df[['symbol', 'target_shares']].set_index('symbol'), hold_df[['symbol', 'available_shares']].set_index('symbol')], axis=1, join='outer')
#     df.fillna(0, inplace=True)
#     df['t0_shares'] = df[['target_shares', 'available_shares']].min(axis=1)    
#     df['t0_shares'] = df['t0_shares'].div(100).astype('int').mul(100)
#     df['t0_shares'] = np.where((df.index >= 688000) & (df['t0_shares'] < 200), 0, df['t0_shares'])
#     df = df[['t0_shares']].reset_index()
#     df = df[df['t0_shares'] != 0]
    
    
#     account_id = '鲲洋远如量化1号证券'
#     algorithm = 'ld_t0'
#     start_time = kwargs.get('start_time', '093000')
#     end_time = kwargs.get('end_time', '145500')
#     trade_date = work_date
#     algorithm_params = ':'.join([f'{key}={value}' for key, value in {}.items()])
    
#     cols = [
#         '算法类型',
#         '账户名称',
#         '算法实例',
#         '证券代码',
#         '任务数量',
#         '买入方向',
#         '卖出方向',
#         '开始时间',
#         '结束时间',
#         '涨跌停设置',
#         '过期后执行',
#         '其他参数',
#         '交易市场',
#     ]
#     order = pd.DataFrame(columns=cols)
#     order['证券代码'] = df['symbol'].astype(str).str.zfill(6).apply(GetExchange)
#     order['任务数量'] = df['t0_shares'].tolist()
#     order['买入方向'] = '买入'
#     order['卖出方向'] = '卖出'
#     order['算法类型'] = 'T0'
#     order['账户名称'] = account_id
#     order['算法实例'] = algorithm
#     order['开始时间'] = '{}T{}000'.format(trade_date, start_time)
#     order['结束时间'] = '{}T{}000'.format(trade_date, end_time)
#     order['涨跌停设置'] = '涨停不买跌停不卖'
#     order['过期后执行'] = '否'
#     order['其他参数'] = algorithm_params
#     order['交易市场'] = df['symbol'].astype(str).str.zfill(6).apply(GetOnlyExchangeCn)
    
    
#     local_account_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'accounts', account_name)
#     csv_file_path = os.path.join(local_account_dir, f'{account_name}_t0_target_{work_date}{order_suffix}.csv')
#     order.to_csv(csv_file_path, index=False, encoding='gbk')
#     print(f't0 orders:\n{order.head(5)}')
    

    
    
def ftpcopy_vwap(work_date, suffix=''):
    local_account_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'accounts', account_name)
    dav_order_dir = os.path.join(f'/home/<USER>/dav', 'accounts', account_name, 'order')

    # vwap
    shutil.copy(os.path.join(local_account_dir, f'{account_name}_vwap_target_{work_date}{suffix}.csv'),
                os.path.join(dav_order_dir, f'{account_name}_vwap_target_{work_date}{suffix}.csv')
                )



def ftpcopy_t0(work_date, suffix=''):
    # # t0
    local_account_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'accounts', account_name)
    dav_order_dir = os.path.join(f'/home/<USER>/dav', 'accounts', account_name, 'order')
    
    shutil.copy(os.path.join(local_account_dir, f'{account_name}_t0_target_{work_date}{suffix}.csv'),
                os.path.join(dav_order_dir, f'{account_name}_t0_target_{work_date}{suffix}.csv')
                )
    



if __name__ == "__main__":
    if len(sys.argv) > 1:
        work_date = str(sys.argv[1])
    else:
        work_date = datetime.datetime.now().strftime('%Y%m%d')
    
    # ===========================
    account_name = '中性对冲_国君股票'
    # hold_type    = 'latest'
    hold_type    = 'from_target'
    # hold_type    = 'as_target'

    target_suffix  = ''
    order_suffix   = ''
    
    vwap_start_time = '093000'
    vwap_end_time   = '100000'
    
    t0_start_time = '093000'
    t0_end_time   = '145500'
    
    
    print(f'\n持仓_type: {hold_type} \n目标_suffix: {target_suffix}  \norder_suffix: {order_suffix}')
    
    print(f'账户: {account_name}\n交易日: {work_date}\n')
    # print(f'\n交易日: {work_date}\n')
    
    # calc_vwap_target_info(work_date, suffix)
    vwap_target(work_date, hold_type, target_suffix, order_suffix, 
                start_time=vwap_start_time, 
                end_time=vwap_end_time
                )
    flag_vwap = input('\n   是否上传 vwap_target？(y/n)\n       ')
    if flag_vwap.lower() == 'y':
        ftpcopy_vwap(work_date, order_suffix)
        # upload_order_to_zt_tuoguan(work_date, 'vwap', order_suffix)
        print(f'\n上传 vwap_target 完成')

    
    t0_target(work_date, hold_type, target_suffix, order_suffix, 
              start_time=t0_start_time, 
              end_time=t0_end_time
              )
    flag_t0 = input('\n   是否上传 t0_target？(y/n)\n       ')
    if flag_t0.lower() == 'y':
        ftpcopy_t0(work_date, order_suffix)
        # upload_order_to_zt_tuoguan(work_date, 't0', order_suffix)
        print(f'\n上传 t0_target 完成')